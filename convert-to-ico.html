<!DOCTYPE html>
<html>
<head>
    <title>Convert PNG to ICO - Tamil Jadhagam Icon</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 3px dashed #ff6b35;
            padding: 40px;
            margin: 20px 0;
            border-radius: 10px;
            background: #fff8f5;
        }
        .upload-area:hover {
            background: #fff3e0;
        }
        input[type="file"] {
            margin: 20px 0;
            padding: 10px;
            border: 2px solid #ff6b35;
            border-radius: 5px;
        }
        .download-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .download-btn:hover {
            background: #e55a2b;
        }
        .download-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        h1 {
            color: #ff6b35;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        .preview {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }
        canvas {
            border: 2px solid #ddd;
            margin: 5px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 PNG to ICO Converter for Tamil "ஜ" Icon</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li><strong>Upload your app-icon.png</strong> file using the button below</li>
                <li><strong>Preview</strong> will show multiple sizes (16x16 to 256x256)</li>
                <li><strong>Download ICO</strong> file for Windows compatibility</li>
                <li><strong>Save as "app-icon.ico"</strong> in src/assets/ folder</li>
                <li><strong>Rebuild</strong> the application to apply the new icon</li>
            </ol>
        </div>

        <div class="upload-area">
            <h3>📁 Upload Your PNG Icon</h3>
            <input type="file" id="fileInput" accept=".png" onchange="loadImage(event)">
            <p>Select your app-icon.png file</p>
        </div>

        <div id="preview" class="preview" style="display: none;">
            <h3>👀 Icon Preview (Multiple Sizes)</h3>
            <div id="canvasContainer"></div>
            <button class="download-btn" onclick="downloadICO()" id="downloadBtn" disabled>
                💾 Download as ICO File
            </button>
        </div>

        <div id="status" style="margin-top: 20px; font-weight: bold;"></div>

        <div class="instructions">
            <h3>🔧 Alternative Method:</h3>
            <p>If the converter doesn't work, use online tools:</p>
            <ul>
                <li><strong>convertio.co</strong> - PNG to ICO converter</li>
                <li><strong>icoconvert.com</strong> - Free ICO converter</li>
                <li><strong>favicon.io</strong> - Icon generator</li>
            </ul>
            <p>Upload your PNG and download as ICO with multiple sizes (16, 32, 48, 64, 128, 256)</p>
        </div>
    </div>

    <script>
        let originalImage = null;
        const sizes = [16, 32, 48, 64, 128, 256];

        function loadImage(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    originalImage = img;
                    createPreviews();
                    document.getElementById('preview').style.display = 'block';
                    document.getElementById('downloadBtn').disabled = false;
                    document.getElementById('status').innerHTML = 
                        '<span style="color: green;">✅ Image loaded! Ready to convert to ICO</span>';
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function createPreviews() {
            const container = document.getElementById('canvasContainer');
            container.innerHTML = '';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.title = `${size}x${size}`;
                
                const ctx = canvas.getContext('2d');
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                
                // Draw the image scaled to this size
                ctx.drawImage(originalImage, 0, 0, size, size);
                
                // Add size label
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                label.style.fontSize = '12px';
                label.style.color = '#666';
                
                const wrapper = document.createElement('div');
                wrapper.style.display = 'inline-block';
                wrapper.style.margin = '10px';
                wrapper.style.textAlign = 'center';
                wrapper.appendChild(canvas);
                wrapper.appendChild(label);
                
                container.appendChild(wrapper);
            });
        }

        function downloadICO() {
            // Since we can't create a real ICO file in browser, 
            // we'll download the 256x256 PNG and provide instructions
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(originalImage, 0, 0, 256, 256);

            const link = document.createElement('a');
            link.download = 'app-icon-256.png';
            link.href = canvas.toDataURL('image/png');
            link.click();

            document.getElementById('status').innerHTML = 
                '<span style="color: blue;">💾 High-quality PNG downloaded!<br>' +
                'Use <strong>convertio.co</strong> or <strong>icoconvert.com</strong> to convert to ICO format.<br>' +
                'Upload the PNG and download as ICO with multiple sizes.</span>';

            // Also provide a direct link to online converter
            setTimeout(() => {
                if (confirm('Open online ICO converter in new tab?')) {
                    window.open('https://convertio.co/png-ico/', '_blank');
                }
            }, 2000);
        }
    </script>
</body>
</html>
