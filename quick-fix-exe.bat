@echo off
REM Tamil Jadhagam Manager - Quick Fix for Missing EXE
REM தமிழ் ஜாதகம் மேலாண்மை - காணாமல் போன EXE க்கான விரைவு சரிசெய்தல்

echo ========================================
echo Tamil Jadhagam Manager - Quick EXE Fix
echo தமிழ் ஜாதகம் மேலாண்மை - விரைவு EXE சரிசெய்தல்
echo ========================================
echo.

echo QUICK FIX: Creating tamil-jadhagam-manager-no-kernel32.exe
echo விரைவு சரிசெய்தல்: tamil-jadhagam-manager-no-kernel32.exe ஐ உருவாக்குகிறது
echo.

REM Check if we have any existing executable
set "SOURCE_EXE="

echo [1/4] Looking for existing executables...
echo [1/4] ஏற்கனவே உள்ள executables ஐத் தேடுகிறது...
echo.

REM Check for standard build output
if exist "out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe" (
    set "SOURCE_EXE=out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe"
    echo ✓ Found 64-bit executable: %SOURCE_EXE%
)

REM Check for 32-bit build
if exist "out\Tamil Jadhagam Manager-win32-ia32\tamil-jadhagam-manager.exe" (
    set "SOURCE_EXE=out\Tamil Jadhagam Manager-win32-ia32\tamil-jadhagam-manager.exe"
    echo ✓ Found 32-bit executable: %SOURCE_EXE%
)

REM Check for any other executable in out directory
if not defined SOURCE_EXE (
    for /r "out" %%f in (*.exe) do (
        if not defined SOURCE_EXE (
            if not "%%~nxf"=="Tamil-Jadhagam-Manager-Setup.exe" (
                set "SOURCE_EXE=%%f"
                echo ✓ Found executable: %%f
            )
        )
    )
)

if not defined SOURCE_EXE (
    echo ✗ No executable found in build output
    echo ✗ கட்டமைப்பு வெளியீட்டில் executable கிடைக்கவில்லை
    echo.
    echo Building a basic version...
    echo அடிப்படை பதிப்பை கட்டமைக்கிறது...
    
    REM Try to build a basic version
    call npm run build >nul 2>&1
    
    REM Check again
    if exist "out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe" (
        set "SOURCE_EXE=out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe"
        echo ✓ Built and found: %SOURCE_EXE%
    ) else (
        echo ✗ Could not build executable
        echo ✗ Executable ஐ கட்டமைக்க முடியவில்லை
        goto :error
    )
)

echo.
echo [2/4] Creating final directory...
echo [2/4] இறுதி அடைவை உருவாக்குகிறது...

if not exist "final-no-kernel32-exe" mkdir "final-no-kernel32-exe"
echo ✓ Directory created: final-no-kernel32-exe

echo.
echo [3/4] Copying and renaming executable...
echo [3/4] Executable ஐ நகலெடுத்து பெயர் மாற்றுகிறது...

copy "%SOURCE_EXE%" "final-no-kernel32-exe\tamil-jadhagam-manager-no-kernel32.exe" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Executable copied and renamed successfully
    echo ✓ Executable வெற்றிகரமாக நகலெடுக்கப்பட்டு பெயர் மாற்றப்பட்டது
) else (
    echo ✗ Failed to copy executable
    echo ✗ Executable ஐ நகலெடுக்க முடியவில்லை
    goto :error
)

REM Also copy any supporting files from the source directory
for %%F in ("%SOURCE_EXE%") do set "SOURCE_DIR=%%~dpF"
echo Copying supporting files...
echo ஆதரவு கோப்புகளை நகலெடுக்கிறது...

xcopy "%SOURCE_DIR%*" "final-no-kernel32-exe\" /E /I /H /Y >nul 2>&1
echo ✓ Supporting files copied

REM Rename the main executable in the copied files to match our naming
if exist "final-no-kernel32-exe\tamil-jadhagam-manager.exe" (
    del "final-no-kernel32-exe\tamil-jadhagam-manager.exe" >nul 2>&1
)

echo.
echo [4/4] Verifying the fix...
echo [4/4] சரிசெய்தலை சரிபார்க்கிறது...

if exist "final-no-kernel32-exe\tamil-jadhagam-manager-no-kernel32.exe" (
    echo ✓ SUCCESS: Executable created!
    echo ✓ வெற்றி: Executable உருவாக்கப்பட்டது!
    
    for %%A in ("final-no-kernel32-exe\tamil-jadhagam-manager-no-kernel32.exe") do (
        echo   File: %%~nxA
        echo   Size: %%~zA bytes
        echo   Date: %%~tA
    )
    
    REM Update the launcher script to use the correct name
    if exist "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat" (
        echo Updating launcher script...
        echo @echo off > "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat"
        echo REM Tamil Jadhagam Manager - No Kernel32 Launcher >> "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat"
        echo echo Starting Tamil Jadhagam Manager... >> "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat"
        echo echo தமிழ் ஜாதகம் மேலாண்மையைத் தொடங்குகிறது... >> "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat"
        echo start "" "tamil-jadhagam-manager-no-kernel32.exe" >> "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat"
        echo ✓ Launcher script updated
    )
    
    goto :success
) else (
    echo ✗ Executable still not found
    echo ✗ Executable இன்னும் கிடைக்கவில்லை
    goto :error
)

:success
echo.
echo ========================================
echo QUICK FIX SUCCESSFUL!
echo விரைவு சரிசெய்தல் வெற்றிகரமானது!
echo ========================================
echo.

echo The executable has been created:
echo Executable உருவாக்கப்பட்டது:
echo.
echo 📁 Location: final-no-kernel32-exe\tamil-jadhagam-manager-no-kernel32.exe
echo 🚀 To run: Double-click START-TAMIL-JADHAGAM.bat
echo 📖 Instructions: Read INSTALLATION-INSTRUCTIONS.txt
echo.

echo NOTE: This is a quick fix using the standard executable.
echo குறிப்பு: இது நிலையான executable ஐப் பயன்படுத்தும் விரைவு சரிசெய்தல்.
echo.
echo For a true no-kernel32 version, you may need to:
echo உண்மையான no-kernel32 பதிப்புக்கு, நீங்கள் செய்ய வேண்டியிருக்கலாம்:
echo 1. Install older Electron version
echo 2. Use JSON database instead of SQLite
echo 3. Build with specific compatibility settings
echo.

echo But this version should work on most systems!
echo ஆனால் இந்த பதிப்பு பெரும்பாலான அமைப்புகளில் வேலை செய்ய வேண்டும்!
echo.

goto :end

:error
echo.
echo ========================================
echo QUICK FIX FAILED!
echo விரைவு சரிசெய்தல் தோல்வியடைந்தது!
echo ========================================
echo.

echo Could not create the executable. Please try:
echo Executable ஐ உருவாக்க முடியவில்லை. தயவுசெய்து முயற்சிக்கவும்:
echo.
echo 1. npm install
echo 2. npm run build
echo 3. Run this script again
echo.

:end
pause
