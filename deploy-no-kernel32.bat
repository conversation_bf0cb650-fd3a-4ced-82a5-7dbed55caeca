@echo off
REM Tamil Jadhagam Manager - Complete No-Kernel32 Deployment Script
REM தமிழ் ஜாதகம் மேலாண்மை - முழுமையான Kernel32 இல்லாத வெளியீட்டு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Complete Deployment
echo தமிழ் ஜாதகம் மேலாண்மை - முழுமையான வெளியீடு
echo ========================================
echo.
echo COMPLETE BUILD AND DEPLOYMENT FOR NO-KERNEL32 SYSTEMS
echo KERNEL32 இல்லாத அமைப்புகளுக்கான முழுமையான கட்டமைப்பு மற்றும் வெளியீடு
echo.

echo This script will:
echo இந்த ஸ்கிரிப்ட் செய்யும்:
echo.
echo 1. Build the no-kernel32 version
echo 2. Process the executable for maximum compatibility
echo 3. Create distribution packages
echo 4. Generate documentation
echo 5. Test the build
echo 6. Create deployment-ready files
echo.
echo 1. Kernel32 இல்லாத பதிப்பை உருவாக்கும்
echo 2. அதிகபட்ச இணக்கத்தன்மைக்காக executable ஐ செயலாக்கும்
echo 3. விநியோக தொகுப்புகளை உருவாக்கும்
echo 4. ஆவணங்களை உருவாக்கும்
echo 5. கட்டமைப்பை சோதிக்கும்
echo 6. வெளியீட்டுக்கு தயார் கோப்புகளை உருவாக்கும்
echo.

set /p CONTINUE="Continue with deployment? (Y/N): "
if /i "%CONTINUE%" NEQ "Y" (
    echo Deployment cancelled.
    echo வெளியீடு ரத்து செய்யப்பட்டது.
    pause
    exit /b 0
)

echo.
echo ========================================
echo PHASE 1: BUILDING NO-KERNEL32 VERSION
echo கட்டம் 1: KERNEL32 இல்லாத பதிப்பை உருவாக்குதல்
echo ========================================
echo.

REM Check if build script exists
if not exist "build-no-kernel32.bat" (
    echo ✗ Build script not found: build-no-kernel32.bat
    echo ✗ கட்டமைப்பு ஸ்கிரிப்ட் கிடைக்கவில்லை: build-no-kernel32.bat
    pause
    exit /b 1
)

echo Running build script...
echo கட்டமைப்பு ஸ்கிரிப்டை இயக்குகிறது...
echo.

call build-no-kernel32.bat
if %ERRORLEVEL% NEQ 0 (
    echo ✗ Build failed
    echo ✗ கட்டமைப்பு தோல்வியடைந்தது
    pause
    exit /b 1
)

echo ✓ Build completed successfully
echo ✓ கட்டமைப்பு வெற்றிகரமாக முடிந்தது

echo.
echo ========================================
echo PHASE 2: PROCESSING EXECUTABLE
echo கட்டம் 2: EXECUTABLE செயலாக்கம்
echo ========================================
echo.

REM Check if processing script exists
if not exist "process-exe-no-kernel32.bat" (
    echo ✗ Processing script not found: process-exe-no-kernel32.bat
    echo ✗ செயலாக்க ஸ்கிரிப்ட் கிடைக்கவில்லை: process-exe-no-kernel32.bat
    pause
    exit /b 1
)

echo Running executable processing...
echo Executable செயலாக்கத்தை இயக்குகிறது...
echo.

call process-exe-no-kernel32.bat
if %ERRORLEVEL% NEQ 0 (
    echo ✗ Processing failed
    echo ✗ செயலாக்கம் தோல்வியடைந்தது
    pause
    exit /b 1
)

echo ✓ Processing completed successfully
echo ✓ செயலாக்கம் வெற்றிகரமாக முடிந்தது

echo.
echo ========================================
echo PHASE 3: TESTING BUILD
echo கட்டம் 3: கட்டமைப்பை சோதித்தல்
echo ========================================
echo.

REM Check if test script exists
if exist "test-no-kernel32.bat" (
    echo Running build tests...
    echo கட்டமைப்பு சோதனைகளை இயக்குகிறது...
    echo.
    
    call test-no-kernel32.bat
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Tests passed
        echo ✓ சோதனைகள் வெற்றியடைந்தன
    ) else (
        echo ! Tests had issues (may be normal)
        echo ! சோதனைகளில் சிக்கல்கள் (சாதாரணமாக இருக்கலாம்)
    )
) else (
    echo ! Test script not found, skipping tests
    echo ! சோதனை ஸ்கிரிப்ட் கிடைக்கவில்லை, சோதனைகளைத் தவிர்க்கிறது
)

echo.
echo ========================================
echo PHASE 4: CREATING DEPLOYMENT PACKAGE
echo கட்டம் 4: வெளியீட்டு தொகுப்பை உருவாக்குதல்
echo ========================================
echo.

set "DEPLOY_DIR=deployment-no-kernel32"
set "DEPLOY_DATE=%DATE:/=-%"
set "DEPLOY_TIME=%TIME::=-%"
set "DEPLOY_TIMESTAMP=%DEPLOY_DATE%_%DEPLOY_TIME: =%"

echo Creating deployment directory...
echo வெளியீட்டு அடைவை உருவாக்குகிறது...

if exist "%DEPLOY_DIR%" rmdir /s /q "%DEPLOY_DIR%" >nul 2>&1
mkdir "%DEPLOY_DIR%" >nul 2>&1
mkdir "%DEPLOY_DIR%\installers" >nul 2>&1
mkdir "%DEPLOY_DIR%\portable" >nul 2>&1
mkdir "%DEPLOY_DIR%\documentation" >nul 2>&1
mkdir "%DEPLOY_DIR%\tools" >nul 2>&1

echo ✓ Deployment directory structure created
echo ✓ வெளியீட்டு அடைவு கட்டமைப்பு உருவாக்கப்பட்டது

echo.
echo Copying files to deployment package...
echo வெளியீட்டு தொகுப்பிற்கு கோப்புகளை நகலெடுக்கிறது...

REM Copy installer if it exists
if exist "out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe" (
    copy "out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe" "%DEPLOY_DIR%\installers\" >nul 2>&1
    echo ✓ Installer copied
)

REM Copy processed files if they exist
if exist "processed-exe\portable" (
    xcopy "processed-exe\portable\*" "%DEPLOY_DIR%\portable\" /E /I /H /Y >nul 2>&1
    echo ✓ Portable version copied
)

REM Copy documentation
if exist "NO_KERNEL32_GUIDE.md" (
    copy "NO_KERNEL32_GUIDE.md" "%DEPLOY_DIR%\documentation\" >nul 2>&1
    echo ✓ Documentation copied
)

if exist "README.md" (
    copy "README.md" "%DEPLOY_DIR%\documentation\" >nul 2>&1
    echo ✓ Main README copied
)

REM Copy tools and scripts
copy "build-no-kernel32.bat" "%DEPLOY_DIR%\tools\" >nul 2>&1
copy "process-exe-no-kernel32.bat" "%DEPLOY_DIR%\tools\" >nul 2>&1
copy "test-no-kernel32.bat" "%DEPLOY_DIR%\tools\" >nul 2>&1
copy "restore-original-files.bat" "%DEPLOY_DIR%\tools\" >nul 2>&1
echo ✓ Tools and scripts copied

echo.
echo ========================================
echo PHASE 5: GENERATING DEPLOYMENT DOCUMENTATION
echo கட்டம் 5: வெளியீட்டு ஆவணங்களை உருவாக்குதல்
echo ========================================
echo.

REM Create deployment README
echo Tamil Jadhagam Manager - No Kernel32 Deployment Package > "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத வெளியீட்டு தொகுப்பு >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ================================================================ >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo Deployment Date: %DATE% %TIME% >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo வெளியீட்டு தேதி: %DATE% %TIME% >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo PACKAGE CONTENTS: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo தொகுப்பு உள்ளடக்கங்கள்: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 📁 installers\ >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    └── Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo        Traditional installer for end users >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo        இறுதி பயனர்களுக்கான பாரம்பரிய நிறுவி >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 📁 portable\ >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    └── Complete portable application >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo        Can be run from any location >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo        எந்த இடத்திலிருந்தும் இயக்க முடியும் >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 📁 documentation\ >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    ├── NO_KERNEL32_GUIDE.md - Detailed technical guide >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    └── README.md - Main application documentation >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 📁 tools\ >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    ├── build-no-kernel32.bat - Build script >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    ├── process-exe-no-kernel32.bat - Processing script >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    ├── test-no-kernel32.bat - Testing script >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo    └── restore-original-files.bat - Restore script >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo SYSTEM COMPATIBILITY: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo கணினி இணக்கத்தன்மை: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ Windows XP SP3 or later >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ 512MB RAM minimum >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ 200MB free disk space >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ No kernel32.dll required >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ No Visual C++ Redistributables needed >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo ✅ Works on very old systems >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo DISTRIBUTION INSTRUCTIONS: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo விநியோக அறிவுறுத்தல்கள்: >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 1. For most users: Provide the installer from installers\ folder >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 2. For portable use: Provide the portable\ folder >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 3. Include documentation\ folder for user reference >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 4. Keep tools\ folder for technical support >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 1. பெரும்பாலான பயனர்களுக்கு: installers\ அடைவிலிருந்து நிறுவியை வழங்கவும் >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 2. Portable பயன்பாட்டிற்கு: portable\ அடைவை வழங்கவும் >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 3. பயனர் குறிப்புக்காக documentation\ அடைவைச் சேர்க்கவும் >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"
echo 4. தொழில்நுட்ப ஆதரவுக்காக tools\ அடைவை வைத்திருங்கள் >> "%DEPLOY_DIR%\DEPLOYMENT_README.txt"

echo ✓ Deployment documentation created
echo ✓ வெளியீட்டு ஆவணங்கள் உருவாக்கப்பட்டன

echo.
echo ========================================
echo PHASE 6: FINAL VERIFICATION
echo கட்டம் 6: இறுதி சரிபார்ப்பு
echo ========================================
echo.

echo Verifying deployment package...
echo வெளியீட்டு தொகுப்பை சரிபார்க்கிறது...

set "VERIFICATION_PASSED=1"

if not exist "%DEPLOY_DIR%\DEPLOYMENT_README.txt" (
    echo ✗ Deployment README missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✓ Deployment README present
)

if exist "%DEPLOY_DIR%\installers\Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe" (
    echo ✓ Installer present
) else (
    echo ! Installer missing (may need to be built separately)
)

if exist "%DEPLOY_DIR%\portable\tamil-jadhagam-manager-no-kernel32.exe" (
    echo ✓ Portable executable present
) else (
    echo ✗ Portable executable missing
    set "VERIFICATION_PASSED=0"
)

if exist "%DEPLOY_DIR%\documentation\NO_KERNEL32_GUIDE.md" (
    echo ✓ Technical documentation present
) else (
    echo ! Technical documentation missing
)

if "%VERIFICATION_PASSED%" EQU "1" (
    echo ✓ Verification passed
    echo ✓ சரிபார்ப்பு வெற்றியடைந்தது
) else (
    echo ! Verification had issues
    echo ! சரிபார்ப்பில் சிக்கல்கள்
)

echo.
echo ========================================
echo DEPLOYMENT COMPLETED!
echo வெளியீடு முடிந்தது!
echo ========================================
echo.
