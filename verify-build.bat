@echo off
REM Tamil Jadhagam Manager - Build Verification Script
REM தமிழ் ஜாதகம் மேலாண்மை - கட்டமைப்பு சரிபார்ப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Build Verification
echo தமிழ் ஜாதகம் மேலாண்மை - கட்டமைப்பு சரிபார்ப்பு
echo ========================================
echo.

echo [1/5] Checking installer files...
if exist "out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe" (
    echo ✓ Installer found: Tamil-Jadhagam-Manager-Setup.exe
    for %%A in ("out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe") do echo   Size: %%~zA bytes
    for %%A in ("out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe") do echo   Date: %%~tA
) else (
    echo ✗ Installer NOT found
    echo   Expected: out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
    goto :error
)

if exist "out\make\squirrel.windows\x64\tamil-jadhagam-manager-1.2.1-full.nupkg" (
    echo ✓ Package found: tamil-jadhagam-manager-1.2.1-full.nupkg
    for %%A in ("out\make\squirrel.windows\x64\tamil-jadhagam-manager-1.2.1-full.nupkg") do echo   Size: %%~zA bytes
) else (
    echo ✗ Package NOT found
)

echo.
echo [2/5] Checking application executable...
if exist "out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe" (
    echo ✓ Application executable found
    for %%A in ("out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe") do echo   Size: %%~zA bytes
) else (
    echo ✗ Application executable NOT found
    goto :error
)

echo.
echo [3/5] Checking icon files...
if exist "src\assets\app-icon.png" (
    echo ✓ PNG icon found: app-icon.png
    for %%A in ("src\assets\app-icon.png") do echo   Size: %%~zA bytes
) else (
    echo ✗ PNG icon NOT found
)

if exist "src\assets\app-icon.ico" (
    echo ✓ ICO icon found: app-icon.ico
    for %%A in ("src\assets\app-icon.ico") do echo   Size: %%~zA bytes
) else (
    echo ⚠ ICO icon NOT found (optional for basic functionality)
)

echo.
echo [4/5] Checking core application files...
set MISSING_FILES=0

if exist "src\index.js" (
    echo ✓ Main process file found
) else (
    echo ✗ Main process file missing
    set /a MISSING_FILES+=1
)

if exist "src\database.js" (
    echo ✓ Database module found
) else (
    echo ✗ Database module missing
    set /a MISSING_FILES+=1
)

if exist "src\index.html" (
    echo ✓ UI file found
) else (
    echo ✗ UI file missing
    set /a MISSING_FILES+=1
)

if exist "src\app.js" (
    echo ✓ Frontend logic found
) else (
    echo ✗ Frontend logic missing
    set /a MISSING_FILES+=1
)

if exist "src\index.css" (
    echo ✓ Stylesheet found
) else (
    echo ✗ Stylesheet missing
    set /a MISSING_FILES+=1
)

if %MISSING_FILES% gtr 0 (
    echo ✗ %MISSING_FILES% core files missing
    goto :error
)

echo.
echo [5/5] Checking documentation...
set DOC_COUNT=0

if exist "README.md" (
    echo ✓ README.md found
    set /a DOC_COUNT+=1
)

if exist "INSTALLATION_GUIDE.md" (
    echo ✓ INSTALLATION_GUIDE.md found
    set /a DOC_COUNT+=1
)

if exist "USER_MANUAL.md" (
    echo ✓ USER_MANUAL.md found
    set /a DOC_COUNT+=1
)

if exist "CHANGELOG.md" (
    echo ✓ CHANGELOG.md found
    set /a DOC_COUNT+=1
)

if exist "ICON_SETUP_GUIDE.md" (
    echo ✓ ICON_SETUP_GUIDE.md found
    set /a DOC_COUNT+=1
)

echo   Documentation files found: %DOC_COUNT%/5

echo.
echo ========================================
echo BUILD VERIFICATION SUCCESSFUL!
echo கட்டமைப்பு சரிபார்ப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.
echo ✅ All required files are present and ready for deployment!
echo ✅ தேவையான அனைத்து கோப்புகளும் உள்ளன மற்றும் வரிசைப்படுத்தலுக்கு தயார்!
echo.
echo INSTALLER LOCATION:
echo %CD%\out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
echo.
echo NEXT STEPS:
echo 1. Install the application using the installer
echo 2. Check desktop shortcut for Tamil "ஜ" icon
echo 3. Launch the application and verify functionality
echo 4. Test document upload, search, and management features
echo.
echo APPLICATION FEATURES:
echo ✓ Fullscreen desktop application
echo ✓ Tamil "ஜ" icon integration
echo ✓ Professional UI with animations
echo ✓ Complete document management (Upload, Search, Download, Delete)
echo ✓ Tamil language support throughout
echo ✓ Custom title bar with window controls
echo.
goto :end

:error
echo.
echo ========================================
echo BUILD VERIFICATION FAILED!
echo கட்டமைப்பு சரிபார்ப்பு தோல்வியடைந்தது!
echo ========================================
echo.
echo ❌ Some required files are missing or build failed
echo ❌ சில தேவையான கோப்புகள் காணவில்லை அல்லது கட்டமைப்பு தோல்வியடைந்தது
echo.
echo TROUBLESHOOTING:
echo 1. Run: npm run build
echo 2. Run: npm run dist
echo 3. Check for error messages
echo 4. Ensure all source files are present
echo.
pause
exit /b 1

:end
echo ========================================
echo Tamil Jadhagam Manager v1.2.1
echo தமிழ் ஜாதகம் மேலாண்மை v1.2.1
echo Ready for deployment! வரிசைப்படுத்தலுக்கு தயார்!
echo ========================================
pause
