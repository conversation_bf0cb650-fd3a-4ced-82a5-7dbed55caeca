const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('node:path');
const fs = require('fs');
const JadhagamDatabase = require('./database');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow;
let database;

const createWindow = () => {
  // Create the browser window in fullscreen mode
  mainWindow = new BrowserWindow({
    fullscreen: true,
    frame: false, // Remove window frame for true fullscreen
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false, // Disable for stability on older systems
      enableRemoteModule: false,
      sandbox: false, // Disable sandbox for compatibility
    },
    title: 'தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager',
    icon: path.join(__dirname, 'assets', 'app-icon.png'), // Custom Tamil "ஜ" icon
    autoHideMenuBar: true, // Hide menu bar
    menuBarVisible: false, // Ensure menu bar is not visible
    titleBarStyle: 'hidden', // Hide title bar
    show: false // Don't show until ready
  });

  // Remove the default menu completely
  mainWindow.setMenu(null);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Set fullscreen after showing
    mainWindow.setFullScreen(true);
  });

  // Load the index.html of the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Handle fullscreen toggle with F11 key
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F11' && input.type === 'keyDown') {
      const isFullScreen = mainWindow.isFullScreen();
      mainWindow.setFullScreen(!isFullScreen);
    }

    // Handle Alt+F4 to close application
    if (input.key === 'F4' && input.alt && input.type === 'keyDown') {
      mainWindow.close();
    }

    // Handle Escape key to minimize or close
    if (input.key === 'Escape' && input.type === 'keyDown') {
      if (mainWindow.isFullScreen()) {
        mainWindow.minimize();
      }
    }
  });

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    database = new JadhagamDatabase();
    await database.initialize();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
  }
};

// IPC Handlers
ipcMain.handle('get-rasi-list', async () => {
  try {
    return await database.getAllRasi();
  } catch (error) {
    console.error('Error getting rasi list:', error);
    throw error;
  }
});

ipcMain.handle('get-nathathiram-list', async () => {
  try {
    return await database.getAllNathathiram();
  } catch (error) {
    console.error('Error getting nathathiram list:', error);
    throw error;
  }
});

ipcMain.handle('get-jadthi-list', async () => {
  try {
    return await database.getAllJadthi();
  } catch (error) {
    console.error('Error getting jadthi list:', error);
    throw error;
  }
});

ipcMain.handle('get-city-list', async () => {
  try {
    return await database.getAllCities();
  } catch (error) {
    console.error('Error getting city list:', error);
    throw error;
  }
});

// File selection handlers
ipcMain.handle('select-document-file', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'ஜாதகம் ஆவணத்தைத் தேர்ந்தெடுக்கவும் - Select Jadhagam Document',
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'Word Documents', extensions: ['doc', 'docx'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled) {
      return { success: false, message: 'File selection cancelled' };
    }

    const filePath = result.filePaths[0];
    const fileName = path.basename(filePath);
    const fileStats = fs.statSync(filePath);

    return {
      success: true,
      file: {
        path: filePath,
        name: fileName,
        size: fileStats.size
      }
    };
  } catch (error) {
    console.error('Error selecting document file:', error);
    return { success: false, message: 'File selection failed: ' + error.message };
  }
});

ipcMain.handle('select-photo-file', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'புகைப்படத்தைத் தேர்ந்தெடுக்கவும் - Select Photo',
      filters: [
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled) {
      return { success: false, message: 'Photo selection cancelled' };
    }

    const filePath = result.filePaths[0];
    const fileName = path.basename(filePath);
    const fileStats = fs.statSync(filePath);

    return {
      success: true,
      file: {
        path: filePath,
        name: fileName,
        size: fileStats.size
      }
    };
  } catch (error) {
    console.error('Error selecting photo file:', error);
    return { success: false, message: 'Photo selection failed: ' + error.message };
  }
});

ipcMain.handle('scan-document', async () => {
  try {
    // Show message about scanning process
    const scanResult = await dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'ஆவணத்தை ஸ்கேன் செய்யவும் - Scan Document',
      message: 'ஸ்கேனர் அறிவுறுத்தல்கள் - Scanner Instructions',
      detail: `1. உங்கள் ஸ்கேனர் அல்லது பிரிண்டரை இயக்கவும்
2. ஜாதக ஆவணத்தை ஸ்கேனர் கண்ணாடியில் வைக்கவும்
3. ஸ்கேனர் மென்பொருளைப் பயன்படுத்தி ஸ்கேன் செய்யவும்
4. PDF அல்லது JPG வடிவத்தில் சேமிக்கவும்
5. சேமித்த கோப்பைத் தேர்ந்தெடுக்க "OK" அழுத்தவும்

1. Turn on your scanner or printer
2. Place the jadhagam document on scanner glass
3. Use scanner software to scan the document
4. Save as PDF or JPG format
5. Press "OK" to select the saved file`,
      buttons: ['OK - கோப்பைத் தேர்ந்தெடுக்கவும்', 'Cancel - ரத்து செய்'],
      defaultId: 0,
      cancelId: 1
    });

    if (scanResult.response === 1) {
      return { success: false, message: 'Scan cancelled' };
    }

    // After user confirms they've scanned, open file dialog
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'ஸ்கேன் செய்யப்பட்ட ஆவணத்தைத் தேர்ந்தெடுக்கவும் - Select Scanned Document',
      filters: [
        { name: 'Scanned Documents', extensions: ['pdf', 'jpg', 'jpeg', 'png'] },
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled) {
      return { success: false, message: 'File selection cancelled' };
    }

    const filePath = result.filePaths[0];
    const fileName = path.basename(filePath);
    const fileStats = fs.statSync(filePath);

    return {
      success: true,
      file: {
        path: filePath,
        name: fileName,
        size: fileStats.size
      },
      message: 'ஸ்கேன் செய்யப்பட்ட ஆவணம் தேர்ந்தெடுக்கப்பட்டது - Scanned document selected successfully'
    };
  } catch (error) {
    console.error('Error scanning document:', error);
    return { success: false, message: 'Document scan failed: ' + error.message };
  }
});

ipcMain.handle('upload-jadhagam', async (event, jadhagamData) => {
  try {
    // Create directories in user data
    const userDataPath = app.getPath('userData');
    const documentsDir = path.join(userDataPath, 'documents');
    const photosDir = path.join(userDataPath, 'photos');

    if (!fs.existsSync(documentsDir)) {
      fs.mkdirSync(documentsDir, { recursive: true });
    }
    if (!fs.existsSync(photosDir)) {
      fs.mkdirSync(photosDir, { recursive: true });
    }

    const timestamp = Date.now();
    let documentPath = null;
    let documentName = null;
    let documentSize = 0;
    let photoPath = null;
    let photoName = null;

    // Handle document file
    if (jadhagamData.document && jadhagamData.document.path) {
      const docFile = jadhagamData.document;
      documentName = docFile.name;
      const safeDocName = documentName.replace(/[<>:"/\\|?*]/g, '_');
      documentPath = path.join(documentsDir, `${timestamp}_${safeDocName}`);

      // Copy document file
      fs.copyFileSync(docFile.path, documentPath);
      documentSize = fs.statSync(documentPath).size;
    }

    // Handle photo file
    if (jadhagamData.photo && jadhagamData.photo.path) {
      const photoFile = jadhagamData.photo;
      photoName = photoFile.name;
      const safePhotoName = photoName.replace(/[<>:"/\\|?*]/g, '_');
      photoPath = path.join(photosDir, `${timestamp}_${safePhotoName}`);

      // Copy photo file
      fs.copyFileSync(photoFile.path, photoPath);
    }

    // Prepare data for database
    const dbData = {
      name: jadhagamData.name,
      rasi: jadhagamData.rasi,
      nathathiram: jadhagamData.nathathiram,
      jadthi: jadhagamData.jadthi || null,
      city: jadhagamData.city || null,
      birth_date: jadhagamData.birth_date || null,
      birth_time: jadhagamData.birth_time || null,
      birth_place: jadhagamData.birth_place || null,
      gender: jadhagamData.gender || null,
      father_name: jadhagamData.father_name || null,
      mother_name: jadhagamData.mother_name || null,
      notes: jadhagamData.notes || null,
      dosham: jadhagamData.dosham || null,
      file_path: documentPath,
      file_name: documentName,
      file_size: documentSize,
      photo_path: photoPath,
      photo_name: photoName
    };

    const id = await database.insertJadhagam(dbData);
    return {
      success: true,
      id,
      message: 'ஜாதகம் வெற்றிகரமாக பதிவேற்றப்பட்டது - Jadhagam uploaded successfully'
    };
  } catch (error) {
    console.error('Error uploading jadhagam:', error);
    return { success: false, message: 'Upload failed: ' + error.message };
  }
});

ipcMain.handle('search-jadhagam', async (event, searchParams) => {
  try {
    const results = await database.searchJadhagam(searchParams);
    return { success: true, data: results };
  } catch (error) {
    console.error('Error searching jadhagam:', error);
    return { success: false, message: 'Search failed: ' + error.message };
  }
});

ipcMain.handle('open-document', async (event, filePath) => {
  try {
    const { shell } = require('electron');

    // Check if file exists first
    if (!fs.existsSync(filePath)) {
      return { success: false, message: 'File not found: ' + filePath };
    }

    // Use shell.openPath with proper path handling
    const result = await shell.openPath(path.resolve(filePath));

    // If result is empty string, it means success
    // If result contains text, it's an error message
    if (result === '') {
      return { success: true };
    } else {
      return { success: false, message: 'Failed to open document: ' + result };
    }
  } catch (error) {
    console.error('Error opening document:', error);
    return { success: false, message: 'Failed to open document: ' + error.message };
  }
});

ipcMain.handle('download-document', async (event, filePath, originalFileName) => {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return { success: false, message: 'File not found: ' + filePath };
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'ஆவணத்தை சேமிக்கவும் - Save Document',
      defaultPath: originalFileName,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (result.canceled) {
      return { success: false, message: 'Download cancelled' };
    }

    // Copy file to selected location
    fs.copyFileSync(filePath, result.filePath);

    return {
      success: true,
      message: 'ஆவணம் வெற்றிகரமாக பதிவிறக்கப்பட்டது - Document downloaded successfully',
      downloadPath: result.filePath
    };
  } catch (error) {
    console.error('Error downloading document:', error);
    return { success: false, message: 'Download failed: ' + error.message };
  }
});

ipcMain.handle('delete-document', async (event, documentId, filePath) => {
  try {
    // Show confirmation dialog
    const result = await dialog.showMessageBox(mainWindow, {
      type: 'warning',
      title: 'ஆவணத்தை நீக்கவும் - Delete Document',
      message: 'இந்த ஆவணத்தை நிரந்தரமாக நீக்க விரும்புகிறீர்களா?\nDo you want to permanently delete this document?',
      detail: 'இந்த செயல்பாட்டை மாற்ற முடியாது.\nThis action cannot be undone.',
      buttons: [
        'நீக்கு - Delete',
        'ரத்து செய் - Cancel'
      ],
      defaultId: 1, // Cancel is default
      cancelId: 1
    });

    if (result.response === 1) {
      return { success: false, message: 'Delete cancelled' };
    }

    // Delete from database first
    await new Promise((resolve, reject) => {
      database.db.run("DELETE FROM jadhagam_documents WHERE id = ?", [documentId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });

    // Delete file from filesystem if it exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return {
      success: true,
      message: 'ஆவணம் வெற்றிகரமாக நீக்கப்பட்டது - Document deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting document:', error);
    return { success: false, message: 'Delete failed: ' + error.message };
  }
});

// Window control handlers
ipcMain.handle('minimize-window', async () => {
  try {
    if (mainWindow) {
      mainWindow.minimize();
    }
    return { success: true };
  } catch (error) {
    console.error('Error minimizing window:', error);
    return { success: false, message: 'Failed to minimize window' };
  }
});

ipcMain.handle('close-window', async () => {
  try {
    if (mainWindow) {
      mainWindow.close();
    }
    return { success: true };
  } catch (error) {
    console.error('Error closing window:', error);
    return { success: false, message: 'Failed to close window' };
  }
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(async () => {
  await initializeDatabase();
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', () => {
  if (database) {
    database.close();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
