@echo off
REM Tamil Jadhagam Manager - Restore Original Files Script
REM தமிழ் ஜாதகம் மேலாண்மை - அசல் கோப்புகள் மீட்டமைப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Restore Original Files
echo தமிழ் ஜாதகம் மேலாண்மை - அசல் கோப்புகள் மீட்டமைப்பு
echo ========================================
echo.

echo [1/3] Checking for backup files...
echo காப்புப் பிரதி கோப்புகளைச் சரிபார்க்கிறது...
echo.

if not exist "src\index.js.backup" (
    echo ✗ No backup files found
    echo ✗ காப்புப் பிரதி கோப்புகள் கிடைக்கவில்லை
    echo.
    echo Nothing to restore. Original files may already be in place.
    echo மீட்டமைக்க எதுவும் இல்லை. அசல் கோப்புகள் ஏற்கனவே இருக்கலாம்.
    pause
    exit /b 0
)

echo ✓ Backup files found
echo ✓ காப்புப் பிரதி கோப்புகள் கண்டுபிடிக்கப்பட்டன

echo [2/3] Restoring original files...
echo அசல் கோப்புகளை மீட்டமைக்கிறது...
echo.

REM Restore original files
copy "src\index.js.backup" "src\index.js" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Restored src\index.js
) else (
    echo ✗ Failed to restore src\index.js
)

copy "src\database.js.backup" "src\database.js" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Restored src\database.js
) else (
    echo ✗ Failed to restore src\database.js
)

copy "package.json.backup" "package.json" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Restored package.json
) else (
    echo ✗ Failed to restore package.json
)

echo [3/3] Cleaning up backup files...
echo காப்புப் பிரதி கோப்புகளை சுத்தம் செய்கிறது...
echo.

del "src\index.js.backup" >nul 2>&1
del "src\database.js.backup" >nul 2>&1
del "package.json.backup" >nul 2>&1

echo ✓ Backup files cleaned up
echo ✓ காப்புப் பிரதி கோப்புகள் சுத்தம் செய்யப்பட்டன

echo.
echo ========================================
echo RESTORE COMPLETED SUCCESSFULLY!
echo மீட்டமைப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.

echo Original files have been restored.
echo அசல் கோப்புகள் மீட்டமைக்கப்பட்டுள்ளன.
echo.

echo You can now build the regular version using:
echo இப்போது வழக்கமான பதிப்பை இதைப் பயன்படுத்தி உருவாக்கலாம்:
echo.
echo   npm run build
echo   npm run dist
echo.

pause
