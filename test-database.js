// Test script for Tamil Jadhagam Database
const JadhagamDatabase = require('./src/database');

async function testDatabase() {
    console.log('Testing Tamil Jadhagam Database...');
    
    try {
        // Initialize database
        const db = new JadhagamDatabase();
        await db.initialize();
        console.log('✓ Database initialized successfully');

        // Test getting Rasi list
        const rasiList = await db.getAllRasi();
        console.log(`✓ Rasi list loaded: ${rasiList.length} items`);
        console.log('Sample Rasi:', rasiList[0]);

        // Test getting Nathathiram list
        const nathathiramList = await db.getAllNathathiram();
        console.log(`✓ Nathathiram list loaded: ${nathathiramList.length} items`);
        console.log('Sample Nathathiram:', nathathiramList[0]);

        // Test getting Jadthi list
        const jadthiList = await db.getAllJadthi();
        console.log(`✓ Jadthi list loaded: ${jadthiList.length} items`);
        console.log('Sample Jadthi:', jadthiList[0]);

        // Test getting City list
        const cityList = await db.getAllCities();
        console.log(`✓ City list loaded: ${cityList.length} items`);
        console.log('Sample City:', cityList[0]);

        // Test inserting a sample Jadhagam
        const sampleData = {
            name: 'Test Person',
            rasi: 'மேஷம்',
            nathathiram: 'அசுவினி',
            jadthi: 'பிராமணர்',
            city: 'சென்னை',
            file_path: '/test/path/sample.pdf',
            file_name: 'sample.pdf',
            file_size: 1024,
            birth_date: '1990-01-01',
            birth_time: '10:30',
            gender: 'ஆண் - Male',
            father_name: 'Test Father',
            mother_name: 'Test Mother',
            notes: 'Test notes'
        };

        const insertId = await db.insertJadhagam(sampleData);
        console.log(`✓ Sample Jadhagam inserted with ID: ${insertId}`);

        // Test searching
        const searchResults = await db.searchJadhagam({ rasi: 'மேஷம்' });
        console.log(`✓ Search test completed: ${searchResults.length} results found`);

        // Close database
        db.close();
        console.log('✓ Database closed successfully');
        
        console.log('\n🎉 All database tests passed!');
        
    } catch (error) {
        console.error('❌ Database test failed:', error);
        process.exit(1);
    }
}

// Run the test
testDatabase();
