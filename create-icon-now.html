<!DOCTYPE html>
<html>
<head>
    <title>Create Tamil J Icon - Download Now</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 3px solid #ff6b35; 
            margin: 20px;
            background: white;
            border-radius: 10px;
        }
        .download-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .download-btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }
        h1 {
            color: #ff6b35;
            margin-bottom: 20px;
        }
        .instructions {
            background: #fff3e0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b35;
            text-align: left;
        }
        .step {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Tamil "ஜ" Icon Creator - Ready to Download!</h1>
        
        <div class="instructions">
            <h3>📋 Quick Steps to Fix Your Desktop Icon:</h3>
            <div class="step">
                <strong>Step 1:</strong> Click "Create & Download Icon" below
            </div>
            <div class="step">
                <strong>Step 2:</strong> Save the downloaded file as "app-icon.png" 
            </div>
            <div class="step">
                <strong>Step 3:</strong> Copy it to: <code>tamil-jadhagam-app/src/assets/app-icon.png</code>
            </div>
            <div class="step">
                <strong>Step 4:</strong> Rebuild: <code>npm run build && npm run dist</code>
            </div>
        </div>

        <canvas id="iconCanvas" width="256" height="256"></canvas>
        <br>
        <button class="download-btn" onclick="createAndDownload()">🎨 Create & Download Tamil "ஜ" Icon</button>
        <button class="download-btn" onclick="downloadICO()">💾 Download as ICO (Windows)</button>

        <div id="status" style="margin-top: 20px; font-weight: bold;"></div>
    </div>

    <script>
        function createIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 256, 256);
            
            // Create gradient background
            const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
            gradient.addColorStop(0, '#ff8c66');
            gradient.addColorStop(0.7, '#ff6b35');
            gradient.addColorStop(1, '#e55a2b');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = '#d4491f';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            // Draw inner highlight
            ctx.strokeStyle = 'rgba(255,255,255,0.4)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(128, 128, 115, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Draw Tamil letter "ஜ" with better positioning
            ctx.fillStyle = 'white';
            ctx.font = 'bold 100px "Noto Sans Tamil", "Tamil Sangam MN", serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Add shadow for the letter
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 6;
            ctx.shadowOffsetX = 3;
            ctx.shadowOffsetY = 3;
            
            // Draw the Tamil letter
            ctx.fillText('ஜ', 128, 120);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Draw bottom text
            ctx.font = 'bold 18px "Noto Sans Tamil", serif';
            ctx.fillStyle = 'rgba(255,255,255,0.95)';
            ctx.fillText('ஜாதகம்', 128, 210);
            
            // Add corner highlights
            ctx.fillStyle = 'rgba(255,255,255,0.7)';
            const corners = [[50, 50], [206, 50], [50, 206], [206, 206]];
            corners.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            return canvas;
        }
        
        function createAndDownload() {
            const canvas = createIcon();
            const link = document.createElement('a');
            link.download = 'app-icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
            
            document.getElementById('status').innerHTML = 
                '<span style="color: green;">✅ Tamil "ஜ" icon downloaded! Now copy it to src/assets/app-icon.png</span>';
        }
        
        function downloadICO() {
            // Create multiple sizes for ICO
            const sizes = [16, 32, 48, 64, 128, 256];
            const canvases = [];
            
            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                // Scale the main icon
                const mainCanvas = createIcon();
                ctx.drawImage(mainCanvas, 0, 0, size, size);
                canvases.push(canvas);
            });
            
            // Download the 256x256 version for now
            const link = document.createElement('a');
            link.download = 'app-icon-256.png';
            link.href = canvases[5].toDataURL('image/png');
            link.click();
            
            document.getElementById('status').innerHTML = 
                '<span style="color: blue;">💾 ICO-ready PNG downloaded! Use online converter to create .ico file</span>';
        }
        
        // Auto-create icon on page load
        window.onload = function() {
            createIcon();
        };
    </script>
</body>
</html>
