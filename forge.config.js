const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: true, // Enable ASAR for compatibility with plugins
    name: 'Tamil Jadhagam Manager',
    executableName: 'tamil-jadhagam-manager',
    appBundleId: 'com.tamiljadhagam.manager',
    appCategoryType: 'public.app-category.productivity',
    icon: './src/assets/app-icon.png',
    arch: 'x64', // Default to 64-bit
    platform: 'win32',
    electronVersion: '22.3.27', // Compatible version
    win32metadata: {
      CompanyName: 'Tamil Jadhagam Solutions',
      ProductName: 'Tamil Jadhagam Manager',
      FileDescription: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் மேலாண்மை',
      OriginalFilename: 'tamil-jadhagam-manager.exe',
      RequestedExecutionLevel: 'asInvoker' // Don't require admin
    }
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'tamil-jadhagam-manager',
        setupExe: 'Tamil-Jadhagam-Manager-Setup.exe',
        authors: 'Tamil Jadhagam Solutions',
        description: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் ஆவண மேலாண்மை அமைப்பு'
      },
      platforms: ['win32']
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['darwin'],
    },
    {
      name: '@electron-forge/maker-deb',
      config: {
        options: {
          maintainer: 'Tamil Jadhagam Solutions',
          homepage: 'https://github.com/tamiljadhagam/manager'
        }
      },
    },
    {
      name: '@electron-forge/maker-rpm',
      config: {
        options: {
          maintainer: 'Tamil Jadhagam Solutions',
          homepage: 'https://github.com/tamiljadhagam/manager'
        }
      },
    },
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {},
    },
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true,
    }),
  ],
};
