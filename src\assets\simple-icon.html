<!DOCTYPE html>
<html>
<head>
    <title>Tamil Jadhagam Icon Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 2px solid #ddd; 
            margin: 10px;
            background: white;
        }
        .download-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #e55a2b;
        }
        .icon-preview {
            text-align: center;
            margin: 20px 0;
        }
        h1 {
            color: #ff6b35;
            text-align: center;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>தமிழ் ஜாதகம் மேலாண்மை - Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <p>1. Click "Generate Icon" to create the Tamil "ஜ" icon</p>
            <p>2. Right-click on the canvas and "Save image as..." to save as PNG</p>
            <p>3. Save as "app-icon.png" in the src/assets folder</p>
            <p>4. The icon features the Tamil letter "ஜ" for ஜாதகம் (Jadhagam)</p>
        </div>

        <div class="icon-preview">
            <canvas id="iconCanvas" width="256" height="256"></canvas>
            <br>
            <button class="download-btn" onclick="generateIcon()">Generate Tamil "ஜ" Icon</button>
            <button class="download-btn" onclick="downloadIcon()">Download PNG</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 256, 256);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#ff6b35');
            gradient.addColorStop(0.5, '#f7931e');
            gradient.addColorStop(1, '#e55a2b');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = '#d4491f';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // Draw inner circle for depth
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(128, 128, 110, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Draw Tamil letter "ஜ"
            ctx.fillStyle = 'white';
            ctx.font = 'bold 120px "Noto Sans Tamil", serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Add shadow for the letter
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            ctx.fillText('ஜ', 128, 128);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Draw bottom text
            ctx.font = 'bold 16px "Noto Sans Tamil", serif';
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.fillText('ஜாதகம்', 128, 220);
            
            // Add corner decorations
            ctx.fillStyle = 'rgba(255,255,255,0.6)';
            [
                [45, 45], [211, 45], [45, 211], [211, 211]
            ].forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            document.getElementById('result').innerHTML = 
                '<p style="color: green; text-align: center;">✓ Tamil "ஜ" icon generated successfully!</p>';
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'app-icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate icon on page load
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
