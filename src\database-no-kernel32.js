const fs = require('fs');
const path = require('path');
const { app } = require('electron');

/**
 * JSON-based database implementation for systems without kernel32.dll
 * This avoids native SQLite3 bindings that may require kernel32.dll
 */
class JadhagamDatabaseNoKernel32 {
    constructor() {
        // Create database in user data directory
        const userDataPath = app.getPath('userData');
        this.dbPath = path.join(userDataPath, 'jadhagam-data.json');
        this.data = {
            jadhagam_documents: [],
            rasi_list: [],
            nathathiram_list: [],
            jadthi_list: [],
            city_list: [],
            metadata: {
                version: '1.0.0',
                created: new Date().toISOString(),
                lastModified: new Date().toISOString()
            }
        };
        this.nextId = 1;
    }

    async initialize() {
        try {
            // Load existing data if file exists
            if (fs.existsSync(this.dbPath)) {
                const fileContent = fs.readFileSync(this.dbPath, 'utf8');
                this.data = JSON.parse(fileContent);
                
                // Find the highest ID to continue sequence
                if (this.data.jadhagam_documents && this.data.jadhagam_documents.length > 0) {
                    this.nextId = Math.max(...this.data.jadhagam_documents.map(doc => doc.id || 0)) + 1;
                }
            } else {
                // Create new database with default data
                await this.insertDefaultData();
                await this.saveData();
            }
            
            console.log('JSON Database initialized successfully (No Kernel32 mode)');
            return Promise.resolve();
        } catch (error) {
            console.error('Error initializing JSON database:', error);
            return Promise.reject(error);
        }
    }

    async saveData() {
        try {
            this.data.metadata.lastModified = new Date().toISOString();
            const jsonData = JSON.stringify(this.data, null, 2);
            fs.writeFileSync(this.dbPath, jsonData, 'utf8');
            return Promise.resolve();
        } catch (error) {
            console.error('Error saving JSON database:', error);
            return Promise.reject(error);
        }
    }

    async insertDefaultData() {
        // Insert default Rasi data
        this.data.rasi_list = [
            { id: 1, rasi_tamil: 'மேஷம்', rasi_english: 'Mesham' },
            { id: 2, rasi_tamil: 'ரிஷபம்', rasi_english: 'Rishabam' },
            { id: 3, rasi_tamil: 'மிதுனம்', rasi_english: 'Mithunam' },
            { id: 4, rasi_tamil: 'கடகம்', rasi_english: 'Kadagam' },
            { id: 5, rasi_tamil: 'சிம்மம்', rasi_english: 'Simmam' },
            { id: 6, rasi_tamil: 'கன்னி', rasi_english: 'Kanni' },
            { id: 7, rasi_tamil: 'துலாம்', rasi_english: 'Thulam' },
            { id: 8, rasi_tamil: 'விருச்சிகம்', rasi_english: 'Viruchigam' },
            { id: 9, rasi_tamil: 'தனுசு', rasi_english: 'Dhanusu' },
            { id: 10, rasi_tamil: 'மகரம்', rasi_english: 'Magaram' },
            { id: 11, rasi_tamil: 'கும்பம்', rasi_english: 'Kumbam' },
            { id: 12, rasi_tamil: 'மீனம்', rasi_english: 'Meenam' }
        ];

        // Insert default Nathathiram data
        this.data.nathathiram_list = [
            { id: 1, nathathiram_tamil: 'அசுவினி', nathathiram_english: 'Aswini' },
            { id: 2, nathathiram_tamil: 'பரணி', nathathiram_english: 'Bharani' },
            { id: 3, nathathiram_tamil: 'கிருத்திகை', nathathiram_english: 'Krittika' },
            { id: 4, nathathiram_tamil: 'ரோகிணி', nathathiram_english: 'Rohini' },
            { id: 5, nathathiram_tamil: 'மிருகசீரிடம்', nathathiram_english: 'Mrigashirsha' },
            { id: 6, nathathiram_tamil: 'திருவாதிரை', nathathiram_english: 'Ardra' },
            { id: 7, nathathiram_tamil: 'புனர்பூசம்', nathathiram_english: 'Punarvasu' },
            { id: 8, nathathiram_tamil: 'பூசம்', nathathiram_english: 'Pushya' },
            { id: 9, nathathiram_tamil: 'ஆயில்யம்', nathathiram_english: 'Ashlesha' },
            { id: 10, nathathiram_tamil: 'மகம்', nathathiram_english: 'Magha' },
            { id: 11, nathathiram_tamil: 'பூரம்', nathathiram_english: 'Purva Phalguni' },
            { id: 12, nathathiram_tamil: 'உத்திரம்', nathathiram_english: 'Uttara Phalguni' },
            { id: 13, nathathiram_tamil: 'ஹஸ்தம்', nathathiram_english: 'Hasta' },
            { id: 14, nathathiram_tamil: 'சித்திரை', nathathiram_english: 'Chitra' },
            { id: 15, nathathiram_tamil: 'சுவாதி', nathathiram_english: 'Swati' },
            { id: 16, nathathiram_tamil: 'விசாகம்', nathathiram_english: 'Vishakha' },
            { id: 17, nathathiram_tamil: 'அனுஷம்', nathathiram_english: 'Anuradha' },
            { id: 18, nathathiram_tamil: 'கேட்டை', nathathiram_english: 'Jyeshtha' },
            { id: 19, nathathiram_tamil: 'மூலம்', nathathiram_english: 'Mula' },
            { id: 20, nathathiram_tamil: 'பூராடம்', nathathiram_english: 'Purva Ashadha' },
            { id: 21, nathathiram_tamil: 'உத்திராடம்', nathathiram_english: 'Uttara Ashadha' },
            { id: 22, nathathiram_tamil: 'திருவோணம்', nathathiram_english: 'Shravana' },
            { id: 23, nathathiram_tamil: 'அவிட்டம்', nathathiram_english: 'Dhanishta' },
            { id: 24, nathathiram_tamil: 'சதயம்', nathathiram_english: 'Shatabhisha' },
            { id: 25, nathathiram_tamil: 'பூரட்டாதி', nathathiram_english: 'Purva Bhadrapada' },
            { id: 26, nathathiram_tamil: 'உத்திரட்டாதி', nathathiram_english: 'Uttara Bhadrapada' },
            { id: 27, nathathiram_tamil: 'ரேவதி', nathathiram_english: 'Revati' }
        ];

        // Insert default Jadthi data
        this.data.jadthi_list = [
            { id: 1, jadthi_tamil: 'பிராமணர்', jadthi_english: 'Brahmin' },
            { id: 2, jadthi_tamil: 'க்ஷத்திரியர்', jadthi_english: 'Kshatriya' },
            { id: 3, jadthi_tamil: 'வைசியர்', jadthi_english: 'Vaishya' },
            { id: 4, jadthi_tamil: 'சூத்திரர்', jadthi_english: 'Shudra' },
            { id: 5, jadthi_tamil: 'அரசர்', jadthi_english: 'Royal' },
            { id: 6, jadthi_tamil: 'வணிகர்', jadthi_english: 'Merchant' },
            { id: 7, jadthi_tamil: 'விவசாயி', jadthi_english: 'Farmer' },
            { id: 8, jadthi_tamil: 'கைவினைஞர்', jadthi_english: 'Artisan' }
        ];

        // Insert default city data
        this.data.city_list = [
            { id: 1, city_tamil: 'சென்னை', city_english: 'Chennai', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 2, city_tamil: 'கோயம்புத்தூர்', city_english: 'Coimbatore', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 3, city_tamil: 'மதுரை', city_english: 'Madurai', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 4, city_tamil: 'திருச்சி', city_english: 'Trichy', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 5, city_tamil: 'சேலம்', city_english: 'Salem', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 6, city_tamil: 'திருநெல்வேலி', city_english: 'Tirunelveli', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 7, city_tamil: 'ஈரோடு', city_english: 'Erode', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 8, city_tamil: 'வேலூர்', city_english: 'Vellore', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 9, city_tamil: 'தூத்துக்குடி', city_english: 'Thoothukudi', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' },
            { id: 10, city_tamil: 'திருவண்ணாமலை', city_english: 'Tiruvannamalai', state_tamil: 'தமிழ்நாடு', state_english: 'Tamil Nadu' }
        ];

        return Promise.resolve();
    }

    // CRUD operations for Jadhagam documents
    async insertJadhagam(data) {
        try {
            const newDocument = {
                id: this.nextId++,
                name: data.name,
                rasi: data.rasi,
                nathathiram: data.nathathiram,
                jadthi: data.jadthi || null,
                city: data.city || null,
                file_path: data.file_path,
                file_name: data.file_name,
                file_size: data.file_size,
                upload_date: new Date().toISOString(),
                birth_date: data.birth_date || null,
                birth_time: data.birth_time || null,
                birth_place: data.birth_place || null,
                gender: data.gender || null,
                father_name: data.father_name || null,
                mother_name: data.mother_name || null,
                notes: data.notes || null,
                dosham: data.dosham || null,
                photo_path: data.photo_path || null,
                photo_name: data.photo_name || null
            };

            this.data.jadhagam_documents.push(newDocument);
            await this.saveData();
            return Promise.resolve(newDocument.id);
        } catch (error) {
            console.error('Error inserting jadhagam:', error);
            return Promise.reject(error);
        }
    }

    async searchJadhagam(searchParams) {
        try {
            let results = [...this.data.jadhagam_documents];

            // Apply filters
            if (searchParams.rasi) {
                results = results.filter(doc =>
                    doc.rasi && doc.rasi.toLowerCase().includes(searchParams.rasi.toLowerCase())
                );
            }
            if (searchParams.nathathiram) {
                results = results.filter(doc =>
                    doc.nathathiram && doc.nathathiram.toLowerCase().includes(searchParams.nathathiram.toLowerCase())
                );
            }
            if (searchParams.jadthi) {
                results = results.filter(doc =>
                    doc.jadthi && doc.jadthi.toLowerCase().includes(searchParams.jadthi.toLowerCase())
                );
            }
            if (searchParams.city) {
                results = results.filter(doc =>
                    doc.city && doc.city.toLowerCase().includes(searchParams.city.toLowerCase())
                );
            }
            if (searchParams.name) {
                results = results.filter(doc =>
                    doc.name && doc.name.toLowerCase().includes(searchParams.name.toLowerCase())
                );
            }
            if (searchParams.dosham) {
                results = results.filter(doc =>
                    doc.dosham && doc.dosham.toLowerCase().includes(searchParams.dosham.toLowerCase())
                );
            }
            if (searchParams.gender) {
                results = results.filter(doc =>
                    doc.gender && doc.gender.toLowerCase().includes(searchParams.gender.toLowerCase())
                );
            }

            // Sort by upload_date DESC
            results.sort((a, b) => new Date(b.upload_date) - new Date(a.upload_date));

            return Promise.resolve(results);
        } catch (error) {
            console.error('Error searching jadhagam:', error);
            return Promise.reject(error);
        }
    }

    async getAllRasi() {
        try {
            return Promise.resolve([...this.data.rasi_list].sort((a, b) => a.id - b.id));
        } catch (error) {
            console.error('Error getting rasi list:', error);
            return Promise.reject(error);
        }
    }

    async getAllNathathiram() {
        try {
            return Promise.resolve([...this.data.nathathiram_list].sort((a, b) => a.id - b.id));
        } catch (error) {
            console.error('Error getting nathathiram list:', error);
            return Promise.reject(error);
        }
    }

    async getAllJadthi() {
        try {
            return Promise.resolve([...this.data.jadthi_list].sort((a, b) => a.id - b.id));
        } catch (error) {
            console.error('Error getting jadthi list:', error);
            return Promise.reject(error);
        }
    }

    async getAllCities() {
        try {
            return Promise.resolve([...this.data.city_list].sort((a, b) => a.city_tamil.localeCompare(b.city_tamil)));
        } catch (error) {
            console.error('Error getting city list:', error);
            return Promise.reject(error);
        }
    }

    async deleteDocument(documentId) {
        try {
            const index = this.data.jadhagam_documents.findIndex(doc => doc.id === documentId);
            if (index !== -1) {
                this.data.jadhagam_documents.splice(index, 1);
                await this.saveData();
                return Promise.resolve(1); // Return number of affected rows
            }
            return Promise.resolve(0);
        } catch (error) {
            console.error('Error deleting document:', error);
            return Promise.reject(error);
        }
    }

    close() {
        // No need to close anything for JSON-based database
        console.log('JSON Database connection closed (No Kernel32 mode)');
    }
}

module.exports = JadhagamDatabaseNoKernel32;
