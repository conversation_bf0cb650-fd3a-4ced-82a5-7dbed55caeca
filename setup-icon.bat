@echo off
REM Tamil Jadhagam Manager - Icon Setup Script
REM தமிழ் ஜாதகம் மேலாண்மை - குறியீடு அமைப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Icon Setup
echo தமிழ் ஜாதகம் மேலாண்மை - குறியீடு அமைப்பு
echo ========================================
echo.

echo [1/4] Checking for icon files...
if exist "src\assets\app-icon.png" (
    echo ✓ app-icon.png found
    for %%A in ("src\assets\app-icon.png") do echo   Size: %%~zA bytes
) else (
    echo ✗ app-icon.png NOT found
    echo.
    echo Please create the icon first:
    echo 1. Open create-icon-now.html in your browser
    echo 2. Click "Create & Download Tamil ஜ Icon"
    echo 3. Save as "app-icon.png" in src\assets\ folder
    echo.
    pause
    exit /b 1
)

if exist "src\assets\app-icon.ico" (
    echo ✓ app-icon.ico found
) else (
    echo ⚠ app-icon.ico NOT found
    echo   This is needed for Windows installer icon
    echo   Use convert-to-ico.html or online converter
)

echo.
echo [2/4] Checking Electron configuration...
findstr /C:"icon:" forge.config.js >nul
if %errorlevel% equ 0 (
    echo ✓ Icon configuration found in forge.config.js
) else (
    echo ✗ Icon configuration missing
)

findstr /C:"app-icon" src\index.js >nul
if %errorlevel% equ 0 (
    echo ✓ Icon reference found in main process
) else (
    echo ✗ Icon reference missing in main process
)

echo.
echo [3/4] Building application with new icon...
echo This may take a few minutes...
call npm run build
if %errorlevel% neq 0 (
    echo ✗ Build failed
    pause
    exit /b 1
)
echo ✓ Build completed successfully

echo.
echo [4/4] Creating installer with Tamil ஜ icon...
echo This may take several minutes...
call npm run dist
if %errorlevel% neq 0 (
    echo ✗ Installer creation failed
    pause
    exit /b 1
)
echo ✓ Installer created successfully

echo.
echo ========================================
echo ICON SETUP COMPLETED SUCCESSFULLY!
echo குறியீடு அமைப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.
echo Your Tamil "ஜ" icon is now ready!
echo உங்கள் தமிழ் "ஜ" குறியீடு இப்போது தயார்!
echo.
echo Installer location:
echo %CD%\out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
echo.
echo Next steps:
echo 1. Install the application using the new installer
echo 2. Check desktop shortcut for Tamil "ஜ" icon
echo 3. Verify taskbar icon shows Tamil letter
echo.
echo If icon still doesn't show:
echo - Restart Windows Explorer (Ctrl+Shift+Esc → Restart explorer.exe)
echo - Clear icon cache: Delete %LOCALAPPDATA%\IconCache.db and restart
echo - Reinstall the application
echo.
echo ========================================
pause
