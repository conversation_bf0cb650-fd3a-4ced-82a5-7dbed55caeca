@echo off
REM Tamil Jadhagam Manager - No Kernel32.dll Build Script
REM தமிழ் ஜாதகம் மேலாண்மை - Kernel32.dll இல்லாத அமைப்பு கட்டமைப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - No Kernel32 Build
echo தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத கட்டமைப்பு
echo ========================================
echo.
echo Building for systems WITHOUT kernel32.dll:
echo Kernel32.dll இல்லாத அமைப்புகளுக்கு கட்டமைக்கப்படுகிறது:
echo - Very old Windows systems
echo - Embedded systems
echo - Custom Windows installations
echo - Systems with missing system DLLs
echo.

echo [1/6] Backing up original files...
echo அசல் கோப்புகளை காப்புப் பிரதி எடுக்கிறது...
echo.

REM Backup original files
if exist "src\index.js.backup" (
    echo Backup already exists, skipping...
) else (
    copy "src\index.js" "src\index.js.backup" >nul 2>&1
    copy "src\database.js" "src\database.js.backup" >nul 2>&1
    copy "package.json" "package.json.backup" >nul 2>&1
    echo ✓ Original files backed up
)

echo [2/6] Switching to no-kernel32 implementation...
echo Kernel32 இல்லாத செயல்படுத்தலுக்கு மாறுகிறது...
echo.

REM Switch to no-kernel32 implementation
copy "src\index-no-kernel32.js" "src\index.js" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Switched to no-kernel32 main file
) else (
    echo ✗ Failed to switch main file
    goto :error
)

echo [3/6] Installing compatible dependencies...
echo இணக்கமான சார்புகளை நிறுவுகிறது...
echo.

REM Remove native dependencies that require kernel32.dll
npm uninstall sqlite3 --save >nul 2>&1
echo ✓ Removed native SQLite3 dependency

REM Install pure JavaScript alternatives
npm install --save better-sqlite3@8.7.0 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Installed compatible database alternative
) else (
    echo ! Warning: Could not install better-sqlite3, using JSON database
)

echo [4/8] Updating package.json for no-kernel32 build...
echo Kernel32 இல்லாத கட்டமைப்புக்கு package.json ஐ புதுப்பிக்கிறது...
echo.

REM Update package.json main entry
powershell -Command "(Get-Content package.json) -replace '\"main\": \"src/index.js\"', '\"main\": \"src/index-no-kernel32.js\"' | Set-Content package.json"
if %ERRORLEVEL% EQU 0 (
    echo ✓ Updated package.json main entry
) else (
    echo ✗ Failed to update package.json
    goto :error
)

echo [5/8] Cleaning previous builds...
echo முந்தைய கட்டமைப்புகளை சுத்தம் செய்கிறது...
echo.

REM Clean previous builds
if exist "out" (
    rmdir /s /q "out" >nul 2>&1
    echo ✓ Cleaned previous build output
)

if exist "dist" (
    rmdir /s /q "dist" >nul 2>&1
    echo ✓ Cleaned previous distribution files
)

echo [6/8] Building application with no-kernel32 configuration...
echo Kernel32 இல்லாத கட்டமைப்புடன் பயன்பாட்டை கட்டமைக்கிறது...
echo.

REM Build with no-kernel32 configuration
call npx electron-forge package --config=./forge.config.no-kernel32.js
if %ERRORLEVEL% EQU 0 (
    echo ✓ Application packaged successfully
) else (
    echo ✗ Failed to package application
    goto :error
)

echo [7/8] Creating installer...
echo நிறுவியை உருவாக்குகிறது...
echo.

call npx electron-forge make --config=./forge.config.no-kernel32.js
if %ERRORLEVEL% EQU 0 (
    echo ✓ Installer created successfully
) else (
    echo ✗ Failed to create installer
    goto :error
)

echo [8/8] Post-processing executable for maximum compatibility...
echo அதிகபட்ச இணக்கத்தன்மைக்காக executable ஐ பின்-செயலாக்கம் செய்கிறது...
echo.

REM Find the built executable
set "BUILT_EXE=out\Tamil Jadhagam Manager No-Kernel32-win32-ia32\tamil-jadhagam-manager-no-kernel32.exe"

if exist "%BUILT_EXE%" (
    echo ✓ Found built executable: %BUILT_EXE%

    REM Get file size
    for %%A in ("%BUILT_EXE%") do set "EXE_SIZE=%%~zA"
    echo ✓ Executable size: %EXE_SIZE% bytes

    REM Create a compatibility report
    echo ✓ Creating compatibility report...
    echo Tamil Jadhagam Manager - No Kernel32 Version > "%BUILT_EXE%.compatibility.txt"
    echo Built on: %DATE% %TIME% >> "%BUILT_EXE%.compatibility.txt"
    echo Electron Version: 9.4.4 >> "%BUILT_EXE%.compatibility.txt"
    echo Architecture: 32-bit (ia32) >> "%BUILT_EXE%.compatibility.txt"
    echo Database: JSON-based (no SQLite) >> "%BUILT_EXE%.compatibility.txt"
    echo Kernel32.dll Required: NO >> "%BUILT_EXE%.compatibility.txt"
    echo Minimum Windows: XP SP3 >> "%BUILT_EXE%.compatibility.txt"
    echo Minimum RAM: 512MB >> "%BUILT_EXE%.compatibility.txt"

) else (
    echo ✗ Built executable not found
    goto :error
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo கட்டமைப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.

echo OUTPUT LOCATION:
echo வெளியீட்டு இடம்:
echo.
echo Installer: out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe
echo Portable: out\Tamil Jadhagam Manager No-Kernel32-win32-ia32\
echo.

echo COMPATIBILITY FEATURES:
echo இணக்கத்தன்மை அம்சங்கள்:
echo.
echo ✓ No kernel32.dll dependency
echo ✓ JSON-based database (no native SQLite)
echo ✓ Ultra-compatible Electron version
echo ✓ 32-bit architecture for maximum compatibility
echo ✓ Minimal system requirements
echo ✓ Works on very old Windows systems
echo.

echo SYSTEM REQUIREMENTS:
echo கணினி தேவைகள்:
echo.
echo • Windows XP SP3 or later
echo • 512MB RAM minimum
echo • 100MB free disk space
echo • No additional dependencies required
echo.

echo INSTALLATION NOTES:
echo நிறுவல் குறிப்புகள்:
echo.
echo • This version works on systems without kernel32.dll
echo • Uses JSON database instead of SQLite
echo • Maximum compatibility with old systems
echo • No Visual C++ Redistributables required
echo • Can run from any location (portable)
echo.

echo • இந்த பதிப்பு kernel32.dll இல்லாத அமைப்புகளில் வேலை செய்கிறது
echo • SQLite க்கு பதிலாக JSON தரவுத்தளத்தைப் பயன்படுத்துகிறது
echo • பழைய அமைப்புகளுடன் அதிகபட்ச இணக்கத்தன்மை
echo • Visual C++ Redistributables தேவையில்லை
echo • எந்த இடத்திலிருந்தும் இயக்க முடியும் (portable)

echo.
echo [RESTORE] To restore original files, run: restore-original-files.bat
echo [மீட்டமை] அசல் கோப்புகளை மீட்டமைக்க இயக்கவும்: restore-original-files.bat
echo.

goto :end

:error
echo.
echo ========================================
echo BUILD FAILED!
echo கட்டமைப்பு தோல்வியடைந்தது!
echo ========================================
echo.
echo Please check the error messages above and try again.
echo மேலே உள்ள பிழை செய்திகளைச் சரிபார்த்து மீண்டும் முயற்சிக்கவும்.
echo.
echo To restore original files, run: restore-original-files.bat
echo அசல் கோப்புகளை மீட்டமைக்க இயக்கவும்: restore-original-files.bat
echo.
pause
exit /b 1

:end
echo Build completed! You can now test the no-kernel32 version.
echo கட்டமைப்பு முடிந்தது! இப்போது kernel32 இல்லாத பதிப்பை சோதிக்கலாம்.
echo.
pause
