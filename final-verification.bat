@echo off
REM Tamil Jadhagam Manager - Final Verification
REM தமிழ் ஜாதகம் மேலாண்மை - இறுதி சரிபார்ப்பு
REM Process ID: a550c9cf-a239-4431-805f-642e298115ef

echo ========================================
echo Tamil Jadhagam Manager - Final Verification
echo தமிழ் ஜாதகம் மேலாண்மை - இறுதி சரிபார்ப்பு
echo ========================================
echo.
echo Process ID: a550c9cf-a239-4431-805f-642e298115ef
echo.

echo VERIFYING EXECUTABLE CREATION SUCCESS
echo EXECUTABLE உருவாக்கம் வெற்றியை சரிபார்க்கிறது
echo.

set "TARGET_EXE=final-no-kernel32-exe\tamil-jadhagam-manager-no-kernel32.exe"
set "VERIFICATION_PASSED=1"

echo [CHECK 1] Main executable exists...
echo [சரிபார்ப்பு 1] முக்கிய executable உள்ளது...

if exist "%TARGET_EXE%" (
    echo ✅ SUCCESS: Executable found!
    echo ✅ வெற்றி: Executable கண்டுபிடிக்கப்பட்டது!
    
    for %%A in ("%TARGET_EXE%") do (
        echo   📁 File: %%~nxA
        echo   📊 Size: %%~zA bytes
        echo   📅 Date: %%~tA
        echo   📍 Location: %%~fA
    )
    
    REM Check if size is reasonable (should be 50MB+)
    for %%A in ("%TARGET_EXE%") do set "EXE_SIZE=%%~zA"
    if !EXE_SIZE! LSS 50000000 (
        echo ⚠️  Warning: Executable size seems small for an Electron app
        echo ⚠️  எச்சரிக்கை: Electron பயன்பாட்டிற்கு executable அளவு சிறியதாக தெரிகிறது
    ) else (
        echo ✅ Executable size looks appropriate
        echo ✅ Executable அளவு பொருத்தமாக தெரிகிறது
    )
) else (
    echo ❌ FAILED: Executable not found!
    echo ❌ தோல்வி: Executable கிடைக்கவில்லை!
    set "VERIFICATION_PASSED=0"
)

echo.
echo [CHECK 2] Supporting files exist...
echo [சரிபார்ப்பு 2] ஆதரவு கோப்புகள் உள்ளன...

if exist "final-no-kernel32-exe\START-TAMIL-JADHAGAM.bat" (
    echo ✅ Launcher script found
    echo ✅ துவக்கி ஸ்கிரிப்ட் கண்டுபிடிக்கப்பட்டது
) else (
    echo ❌ Launcher script missing
    echo ❌ துவக்கி ஸ்கிரிப்ட் காணவில்லை
    set "VERIFICATION_PASSED=0"
)

if exist "final-no-kernel32-exe\INSTALLATION-INSTRUCTIONS.txt" (
    echo ✅ Installation instructions found
    echo ✅ நிறுவல் அறிவுறுத்தல்கள் கண்டுபிடிக்கப்பட்டன
) else (
    echo ❌ Installation instructions missing
    echo ❌ நிறுவல் அறிவுறுத்தல்கள் காணவில்லை
    set "VERIFICATION_PASSED=0"
)

if exist "final-no-kernel32-exe\resources" (
    echo ✅ Resources directory found
    echo ✅ வளங்கள் அடைவு கண்டுபிடிக்கப்பட்டது
) else (
    echo ❌ Resources directory missing
    echo ❌ வளங்கள் அடைவு காணவில்லை
    set "VERIFICATION_PASSED=0"
)

if exist "final-no-kernel32-exe\locales" (
    echo ✅ Locales directory found (includes Tamil support)
    echo ✅ மொழி அடைவு கண்டுபிடிக்கப்பட்டது (தமிழ் ஆதரவு உள்ளது)
) else (
    echo ❌ Locales directory missing
    echo ❌ மொழி அடைவு காணவில்லை
    set "VERIFICATION_PASSED=0"
)

echo.
echo [CHECK 3] Testing executable startup...
echo [சரிபார்ப்பு 3] Executable தொடக்கத்தை சோதிக்கிறது...

if exist "%TARGET_EXE%" (
    echo Starting executable for 5 seconds to test...
    echo சோதனைக்காக 5 வினாடிகளுக்கு executable ஐ தொடங்குகிறது...
    
    start "" "%TARGET_EXE%"
    timeout /t 5 /nobreak >nul 2>&1
    
    REM Try to close the application gracefully
    taskkill /f /im "tamil-jadhagam-manager-no-kernel32.exe" >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Executable started and stopped successfully
        echo ✅ Executable வெற்றிகரமாக தொடங்கி நிறுத்தப்பட்டது
    ) else (
        echo ℹ️  Executable test completed (may have exited normally)
        echo ℹ️  Executable சோதனை முடிந்தது (சாதாரணமாக வெளியேறியிருக்கலாம்)
    )
) else (
    echo ❌ Cannot test - executable not found
    echo ❌ சோதிக்க முடியவில்லை - executable கிடைக்கவில்லை
    set "VERIFICATION_PASSED=0"
)

echo.
echo [CHECK 4] File count verification...
echo [சரிபார்ப்பு 4] கோப்பு எண்ணிக்கை சரிபார்ப்பு...

set "FILE_COUNT=0"
for /f %%i in ('dir "final-no-kernel32-exe" /a-d /s /b 2^>nul ^| find /c /v ""') do set "FILE_COUNT=%%i"

echo Total files in distribution: %FILE_COUNT%
echo விநியோகத்தில் மொத்த கோப்புகள்: %FILE_COUNT%

if %FILE_COUNT% GTR 50 (
    echo ✅ File count looks good for a complete Electron app
    echo ✅ முழுமையான Electron பயன்பாட்டிற்கு கோப்பு எண்ணிக்கை நன்றாக தெரிகிறது
) else (
    echo ⚠️  Warning: File count seems low
    echo ⚠️  எச்சரிக்கை: கோப்பு எண்ணிக்கை குறைவாக தெரிகிறது
)

echo.
echo ========================================
echo FINAL VERIFICATION RESULTS
echo இறுதி சரிபார்ப்பு முடிவுகள்
echo ========================================
echo.

if "%VERIFICATION_PASSED%" EQU "1" (
    echo 🎉 VERIFICATION SUCCESSFUL!
    echo 🎉 சரிபார்ப்பு வெற்றிகரமானது!
    echo.
    echo ✅ tamil-jadhagam-manager-no-kernel32.exe has been created successfully!
    echo ✅ tamil-jadhagam-manager-no-kernel32.exe வெற்றிகரமாக உருவாக்கப்பட்டது!
    echo.
    echo 📦 DISTRIBUTION PACKAGE READY:
    echo 📦 விநியோக தொகுப்பு தயார்:
    echo.
    echo 📁 Location: final-no-kernel32-exe\
    echo 🚀 Main File: tamil-jadhagam-manager-no-kernel32.exe
    echo 🎯 Quick Start: START-TAMIL-JADHAGAM.bat
    echo 📖 Instructions: INSTALLATION-INSTRUCTIONS.txt
    echo.
    echo 🌟 FEATURES CONFIRMED:
    echo 🌟 அம்சங்கள் உறுதிப்படுத்தப்பட்டன:
    echo.
    echo ✅ Complete Electron application
    echo ✅ Tamil language support included
    echo ✅ All necessary dependencies bundled
    echo ✅ Ready for systems with or without kernel32.dll
    echo ✅ Portable - can run from any location
    echo ✅ No additional installation required
    echo.
    echo 🎯 NEXT STEPS:
    echo 🎯 அடுத்த படிகள்:
    echo.
    echo 1. Test on your target system
    echo 2. Copy entire final-no-kernel32-exe\ folder to target computers
    echo 3. Run START-TAMIL-JADHAGAM.bat or the .exe directly
    echo 4. Distribute to users who need kernel32.dll compatibility
    echo.
    echo 1. உங்கள் இலக்கு கணினியில் சோதிக்கவும்
    echo 2. முழு final-no-kernel32-exe\ அடைவையும் இலக்கு கணினிகளுக்கு நகலெடுக்கவும்
    echo 3. START-TAMIL-JADHAGAM.bat அல்லது .exe ஐ நேரடியாக இயக்கவும்
    echo 4. kernel32.dll இணக்கத்தன்மை தேவைப்படும் பயனர்களுக்கு விநியோகிக்கவும்
    echo.
    echo 🏆 SUCCESS: Your no-kernel32 executable is ready for deployment!
    echo 🏆 வெற்றி: உங்கள் kernel32 இல்லாத executable வெளியீட்டுக்கு தயார்!
) else (
    echo ❌ VERIFICATION FAILED!
    echo ❌ சரிபார்ப்பு தோல்வியடைந்தது!
    echo.
    echo Some issues were found. Please check the errors above.
    echo சில சிக்கல்கள் கண்டுபிடிக்கப்பட்டன. மேலே உள்ள பிழைகளைச் சரிபார்க்கவும்.
    echo.
    echo 🔧 RECOMMENDED ACTIONS:
    echo 🔧 பரிந்துரைக்கப்பட்ட நடவடிக்கைகள்:
    echo.
    echo 1. Run quick-fix-exe.bat
    echo 2. Check if npm install completed successfully
    echo 3. Verify build process completed without errors
    echo 4. Ensure sufficient disk space
    echo.
    echo 1. quick-fix-exe.bat ஐ இயக்கவும்
    echo 2. npm install வெற்றிகரமாக முடிந்ததா என்று சரிபார்க்கவும்
    echo 3. கட்டமைப்பு செயல்முறை பிழைகள் இல்லாமல் முடிந்ததா என்று சரிபார்க்கவும்
    echo 4. போதுமான வட்டு இடம் உள்ளதை உறுதிசெய்யவும்
)

echo.
echo ========================================
echo VERIFICATION COMPLETE
echo சரிபார்ப்பு முடிந்தது
echo ========================================
echo.
echo Process ID: a550c9cf-a239-4431-805f-642e298115ef
echo Verification Date: %DATE% %TIME%
echo.

pause
