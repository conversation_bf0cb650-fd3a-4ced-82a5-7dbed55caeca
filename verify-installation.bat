@echo off
REM Tamil Jadhagam Manager - Installation Verification Script
REM தமிழ் ஜாதகம் மேலாண்மை - நிறுவல் சரிபார்ப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Installation Verification
echo தமிழ் ஜாதகம் மேலாண்மை - நிறுவல் சரிபார்ப்பு
echo ========================================
echo.

REM Check if installer exists
echo [1/5] Checking installer file...
set INSTALLER_PATH=%CD%\out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
if exist "%INSTALLER_PATH%" (
    echo ✓ Installer found: Tamil-Jadhagam-Manager-Setup.exe
    for %%A in ("%INSTALLER_PATH%") do echo   Size: %%~zA bytes
    for %%A in ("%INSTALLER_PATH%") do echo   Date: %%~tA
) else (
    echo ✗ Installer not found at expected location
    echo   Expected: %INSTALLER_PATH%
    echo   Please run deploy.bat first to create the installer
    pause
    exit /b 1
)

REM Check if package exists
echo [2/5] Checking package file...
set PACKAGE_PATH=%CD%\out\make\squirrel.windows\x64\tamil-jadhagam-manager-1.0.0-full.nupkg
if exist "%PACKAGE_PATH%" (
    echo ✓ Package found: tamil-jadhagam-manager-1.0.0-full.nupkg
    for %%A in ("%PACKAGE_PATH%") do echo   Size: %%~zA bytes
) else (
    echo ✗ Package not found at expected location
    echo   Expected: %PACKAGE_PATH%
)

REM Check if application build exists
echo [3/5] Checking application build...
set APP_PATH=%CD%\out\Tamil Jadhagam Manager-win32-x64\tamil-jadhagam-manager.exe
if exist "%APP_PATH%" (
    echo ✓ Application executable found
    for %%A in ("%APP_PATH%") do echo   Size: %%~zA bytes
) else (
    echo ✗ Application executable not found
    echo   Expected: %APP_PATH%
)

REM Check documentation files
echo [4/5] Checking documentation...
set DOCS_FOUND=0

if exist "%CD%\README.md" (
    echo ✓ README.md found
    set /a DOCS_FOUND+=1
) else (
    echo ✗ README.md missing
)

if exist "%CD%\INSTALLATION_GUIDE.md" (
    echo ✓ INSTALLATION_GUIDE.md found
    set /a DOCS_FOUND+=1
) else (
    echo ✗ INSTALLATION_GUIDE.md missing
)

if exist "%CD%\USER_MANUAL.md" (
    echo ✓ USER_MANUAL.md found
    set /a DOCS_FOUND+=1
) else (
    echo ✗ USER_MANUAL.md missing
)

if exist "%CD%\PROJECT_SUMMARY.md" (
    echo ✓ PROJECT_SUMMARY.md found
    set /a DOCS_FOUND+=1
) else (
    echo ✗ PROJECT_SUMMARY.md missing
)

echo   Documentation files found: %DOCS_FOUND%/4

REM Check source files
echo [5/5] Checking source files...
set SOURCE_FOUND=0

if exist "%CD%\src\index.js" (
    echo ✓ Main process file found
    set /a SOURCE_FOUND+=1
) else (
    echo ✗ Main process file missing
)

if exist "%CD%\src\database.js" (
    echo ✓ Database module found
    set /a SOURCE_FOUND+=1
) else (
    echo ✗ Database module missing
)

if exist "%CD%\src\index.html" (
    echo ✓ UI file found
    set /a SOURCE_FOUND+=1
) else (
    echo ✗ UI file missing
)

if exist "%CD%\src\app.js" (
    echo ✓ Frontend logic found
    set /a SOURCE_FOUND+=1
) else (
    echo ✗ Frontend logic missing
)

if exist "%CD%\package.json" (
    echo ✓ Package configuration found
    set /a SOURCE_FOUND+=1
) else (
    echo ✗ Package configuration missing
)

echo   Source files found: %SOURCE_FOUND%/5

echo.
echo ========================================
echo VERIFICATION SUMMARY
echo ========================================
echo.

REM Overall status
if exist "%INSTALLER_PATH%" (
    if %DOCS_FOUND% geq 3 (
        if %SOURCE_FOUND% geq 4 (
            echo ✅ VERIFICATION PASSED
            echo.
            echo The Tamil Jadhagam Manager is ready for deployment!
            echo.
            echo Ready to distribute:
            echo - Installer: Tamil-Jadhagam-Manager-Setup.exe
            echo - Documentation: Installation and user guides
            echo - Source code: Complete and organized
            echo.
            echo INSTALLATION INSTRUCTIONS:
            echo 1. Copy Tamil-Jadhagam-Manager-Setup.exe to target machine
            echo 2. Run as administrator
            echo 3. Follow installation wizard
            echo 4. Launch from Start Menu
            echo.
            echo DOCUMENTATION TO SHARE:
            echo - INSTALLATION_GUIDE.md
            echo - USER_MANUAL.md
            echo.
        ) else (
            echo ⚠️  VERIFICATION WARNING
            echo Source files incomplete - development may be affected
        )
    ) else (
        echo ⚠️  VERIFICATION WARNING
        echo Documentation incomplete - user experience may be affected
    )
) else (
    echo ❌ VERIFICATION FAILED
    echo Installer not found - please run deploy.bat first
)

echo.
echo ========================================
echo TESTING RECOMMENDATIONS
echo ========================================
echo.
echo Before distributing, please test:
echo.
echo 1. INSTALLER TESTING:
echo    - Run installer on clean Windows machine
echo    - Verify installation completes without errors
echo    - Check Start Menu integration
echo    - Verify uninstaller works
echo.
echo 2. APPLICATION TESTING:
echo    - Launch application successfully
echo    - Test Tamil text display
echo    - Upload a sample document
echo    - Perform search operations
echo    - Verify document opening
echo.
echo 3. DOCUMENTATION REVIEW:
echo    - Read through user manual
echo    - Verify installation guide accuracy
echo    - Check for any missing information
echo.
echo 4. USER ACCEPTANCE:
echo    - Test with actual Tamil users
echo    - Verify cultural accuracy
echo    - Collect feedback for improvements
echo.
echo ========================================
echo Tamil Jadhagam Manager v1.0.0
echo தமிழ் ஜாதகம் மேலாண்மை v1.0.0
echo Verification completed!
echo ========================================
echo.
pause
