# Tamil Jadhagam Manager - User Manual
## தமிழ் ஜாதகம் மேலாண்மை - பயனர் கையேடு

### Table of Contents - உள்ளடக்கம்

1. [Getting Started - தொடங்குதல்](#getting-started)
2. [Interface Overview - இடைமுகம் மேலோட்டம்](#interface-overview)
3. [Upload Workflow - பதிவேற்ற பணிப்பாய்வு](#upload-workflow)
4. [Search Workflow - தேடல் பணிப்பாய்வு](#search-workflow)
5. [Data Management - தரவு மேலாண்மை](#data-management)
6. [Best Practices - சிறந்த நடைமுறைகள்](#best-practices)

---

### Getting Started - தொடங்குதல்

#### First Launch - முதல் துவக்கம்

When you first open Tamil Jadhagam Manager, you'll see:

1. **Header Section**: 
   - Title in Tamil and English
   - Orange gradient background
   - Application branding

2. **Navigation Tabs**:
   - பதிவேற்றம் - Upload (active by default)
   - தேடல் - Search
   - முடிவுகள் - Results

3. **Main Content Area**:
   - Upload form with Tamil labels
   - Input fields for personal and astrological data

#### Initial Setup Verification

✅ **Check Tamil Font Rendering**: Ensure Tamil text displays correctly
✅ **Database Connection**: App should load without errors
✅ **Dropdown Data**: All dropdowns should populate with Tamil options

---

### Interface Overview - இடைமுகம் மேலோட்டம்

#### Header Design
```
தமிழ் ஜாதகம் மேலாண்மை
Tamil Jadhagam Management System
```
- Bilingual title for accessibility
- Professional orange color scheme
- Clear, readable fonts

#### Navigation System
- **Tab-based Navigation**: Easy switching between functions
- **Active State Indicators**: Orange underline for current tab
- **Responsive Design**: Works on different screen sizes

#### Form Layout
- **Two-column Layout**: Efficient use of space
- **Required Field Indicators**: Asterisk (*) for mandatory fields
- **Consistent Spacing**: Professional appearance
- **Tamil-English Labels**: Bilingual support throughout

---

### Upload Workflow - பதிவேற்ற பணிப்பாய்வு

#### Step 1: Personal Information - தனிப்பட்ட தகவல்

**Required Fields**:
- **பெயர் - Name**: Full name of the person
- **பாலினம் - Gender**: Optional dropdown selection

**Best Practices**:
- Use full names for better searchability
- Include titles if culturally appropriate
- Ensure correct spelling for future searches

#### Step 2: Astrological Data - ஜோதிட தரவு

**Required Selections**:

1. **ராசி - Rasi** (Zodiac Sign):
   ```
   மேஷம், ரிஷபம், மிதுனம், கடகம், சிம்மம், கன்னி,
   துலாம், விருச்சிகம், தனுசு, மகரம், கும்பம், மீனம்
   ```

2. **நட்சத்திரம் - Nathathiram** (Star Constellation):
   ```
   அசுவினி, பரணி, கிருத்திகை, ரோகிணி, மிருகசீரிடம்,
   திருவாதிரை, புனர்பூசம், பூசம், ஆயில்யம், மகம்,
   பூரம், உத்திரம், ஹஸ்தம், சித்திரை, சுவாதி,
   விசாகம், அனுஷம், கேட்டை, மூலம், பூராடம்,
   உத்திராடம், திருவோணம், அவிட்டம், சதயம்,
   பூரட்டாதி, உத்திரட்டாதி, ரேவதி
   ```

#### Step 3: Social Information - சமூக தகவல்

**Required Selections**:

1. **ஜாதி - Jadthi** (Community):
   ```
   பிராமணர், க்ஷத்திரியர், வைசியர், சூத்திரர்,
   அரசர், வணிகர், விவசாயி, கைவினைஞர்
   ```

2. **நகரம் - City**:
   ```
   சென்னை, கோயம்புத்தூர், மதுரை, திருச்சி, சேலம்,
   திருநெல்வேலி, ஈரோடு, வேலூர், தூத்துக்குடி,
   திருவண்ணாமலை
   ```

#### Step 4: Optional Information - விருப்ப தகவல்

- **பிறந்த தேதி - Birth Date**: Date picker format
- **பிறந்த நேரம் - Birth Time**: Time picker (24-hour format)
- **தந்தையின் பெயர் - Father's Name**: Text input
- **தாயின் பெயர் - Mother's Name**: Text input
- **குறிப்புகள் - Notes**: Multi-line text area

#### Step 5: Document Upload - ஆவண பதிவேற்றம்

1. **Click Upload Button**: "ஜாதகம் பதிவேற்றம் - Upload Jadhagam"
2. **File Dialog Opens**: Select document file
3. **Supported Formats**:
   - PDF files (.pdf)
   - Image files (.jpg, .jpeg, .png, .gif)
   - All files (*.*) - use with caution

4. **File Processing**:
   - File copied to secure location
   - Database entry created
   - Success message displayed

#### Upload Success Indicators

✅ **Green Success Message**: "ஜாதகம் வெற்றிகரமாக பதிவேற்றப்பட்டது"
✅ **Form Reset**: All fields cleared for next entry
✅ **Database Update**: Record immediately searchable

---

### Search Workflow - தேடல் பணிப்பாய்வு

#### Search Interface - தேடல் இடைமுகம்

**Search Fields Available**:
- **பெயர் - Name**: Free text search (partial matching)
- **ராசி - Rasi**: Dropdown selection
- **நட்சத்திரம் - Nathathiram**: Dropdown selection
- **ஜாதி - Jadthi**: Dropdown selection
- **நகரம் - City**: Dropdown selection

#### Search Strategies - தேடல் உத்திகள்

**1. Exact Match Search**:
- Select specific values from dropdowns
- Use complete names
- Best for precise results

**2. Partial Name Search**:
- Enter part of the name
- Case-insensitive matching
- Useful when spelling uncertain

**3. Category-based Search**:
- Search by astrological criteria only
- Find all people with same Rasi/Nathathiram
- Useful for astrological analysis

**4. Combined Search**:
- Use multiple criteria together
- Narrow down results effectively
- Most powerful search method

#### Search Execution - தேடல் செயல்படுத்தல்

1. **Enter Criteria**: Fill desired search fields
2. **Click Search**: "தேடல் - Search" button
3. **View Results**: Automatic switch to Results tab
4. **Result Count**: Displayed in success message

#### Search Results Display - தேடல் முடிவுகள் காட்சி

**Result Card Layout**:
```
┌─────────────────────────────────────┐
│ [Name]                    [Date]    │
├─────────────────────────────────────┤
│ ராசி: [Rasi]    நட்சத்திரம்: [Star] │
│ ஜாதி: [Jadthi]  நகரம்: [City]      │
│ [Additional Info if available]      │
├─────────────────────────────────────┤
│           [Open Document]           │
└─────────────────────────────────────┘
```

**Information Displayed**:
- Person's name (header)
- Upload date (top right)
- All astrological and social data
- Birth information (if provided)
- Notes (if provided)
- Three action buttons:
  - **Open Document**: Opens file with default application
  - **Download**: Saves a copy to chosen location
  - **Delete**: Permanently removes the document (with confirmation)

#### Document Actions Detailed - ஆவண செயல்கள் விரிவாக

**1. Open Document - ஆவணத்தைத் திற**:
- Opens the document with your system's default application
- Requires appropriate software (PDF reader for PDFs, image viewer for images)
- Document opens in read-only mode for viewing

**2. Download Document - ஆவணம் பதிவிறக்கம்**:
- Creates a copy of the document at your chosen location
- Shows Windows save dialog with file type filters
- Preserves the original filename
- Useful for creating backups or sharing documents
- Original document remains in the system

**3. Delete Document - ஆவணம் நீக்குதல்**:
- **⚠️ PERMANENT ACTION**: This cannot be undone!
- Shows bilingual confirmation dialog (Tamil and English)
- Removes the document from both database AND file system
- Updates search results immediately after deletion
- **Use with extreme caution** - deleted documents cannot be recovered
- Recommended to download a backup before deleting

**Safety Tips for Deletion**:
- Always verify you're deleting the correct document
- Consider downloading a backup first
- Double-check the confirmation dialog
- Remember: deletion is permanent and irreversible

---

### Data Management - தரவு மேலாண்மை

#### Database Structure - தரவுத்தள அமைப்பு

**Main Tables**:
1. **jadhagam_documents**: Primary data storage
2. **rasi_list**: Zodiac signs reference
3. **nathathiram_list**: Star constellations reference
4. **jadthi_list**: Community categories reference
5. **city_list**: Location reference

#### File Organization - கோப்பு அமைப்பு

**Storage Location**:
```
%APPDATA%/Tamil Jadhagam Manager/
├── jadhagam.db (SQLite database)
└── documents/
    ├── [timestamp]_[original_filename]
    ├── [timestamp]_[original_filename]
    └── ...
```

**File Naming Convention**:
- Format: `[Unix_Timestamp]_[Original_Filename]`
- Example: `1703123456789_raman_jadhagam.pdf`
- Prevents naming conflicts
- Maintains original filename reference

#### Data Integrity - தரவு ஒருமைப்பாடு

**Automatic Safeguards**:
- Required field validation
- File format verification
- Database transaction safety
- Duplicate prevention

**Manual Verification**:
- Regular backup checks
- Search result accuracy
- File accessibility tests

---

### Best Practices - சிறந்த நடைமுறைகள்

#### Data Entry Guidelines - தரவு உள்ளீட்டு வழிகாட்டுதல்கள்

**1. Consistent Naming**:
- Use full names consistently
- Maintain spelling standards
- Include family names when appropriate

**2. Complete Information**:
- Fill all available fields
- Add meaningful notes
- Include birth details when known

**3. Quality Documents**:
- Use clear, readable scans
- Prefer PDF format for text documents
- Ensure adequate resolution for images

#### Search Optimization - தேடல் மேம்படுத்தல்

**1. Start Broad, Then Narrow**:
- Begin with category searches
- Add specific criteria gradually
- Use name search for final filtering

**2. Use Partial Matching**:
- Try different name variations
- Account for spelling differences
- Use common name components

**3. Regular Maintenance**:
- Verify search accuracy periodically
- Update information as needed
- Clean up duplicate entries

#### Backup Strategy - காப்புப் பிரதி உத்தி

**Daily Practice**:
- Close application properly
- Verify successful uploads
- Note any error messages

**Weekly Backup**:
- Copy entire data folder
- Verify backup integrity
- Store in multiple locations

**Monthly Review**:
- Check database performance
- Review storage usage
- Update documentation

#### Security Considerations - பாதுகாப்பு கருத்துக்கள்

**Data Protection**:
- Limit access to authorized users
- Use strong Windows passwords
- Consider folder encryption for sensitive data

**Privacy Measures**:
- Be mindful of personal information
- Follow local privacy laws
- Obtain consent for data storage

**Access Control**:
- Regular user account reviews
- Monitor application usage
- Maintain audit trails

---

### Troubleshooting Quick Reference - விரைவு சிக்கல் தீர்வு

| Issue | Tamil | Solution |
|-------|-------|----------|
| Tamil text garbled | தமிழ் எழுத்துகள் சிதைந்துள்ளன | Install Tamil fonts |
| Upload fails | பதிவேற்றம் தோல்வி | Check file size/format |
| Search returns nothing | தேடல் எதுவும் கிடைக்கவில்லை | Try broader criteria |
| App won't start | பயன்பாடு தொடங்காது | Run as administrator |
| Database error | தரவுத்தள பிழை | Check file permissions |

---

**For additional support, refer to the Installation Guide and README files.**

**தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager v1.0.0**
