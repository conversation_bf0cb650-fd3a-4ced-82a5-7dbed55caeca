@echo off
REM Tamil Jadhagam Manager - Complete No-Kernel32 Process
REM தமிழ் ஜாதகம் மேலாண்மை - முழுமையான Kernel32 இல்லாத செயல்முறை
REM Process ID: a550c9cf-a239-4431-805f-642e298115ef

echo ========================================
echo Tamil Jadhagam Manager - Complete Process
echo தமிழ் ஜாதகம் மேலாண்மை - முழுமையான செயல்முறை
echo ========================================
echo.
echo Process ID: a550c9cf-a239-4431-805f-642e298115ef
echo.
echo COMPLETE EXECUTABLE PROCESS FOR NO-KERNEL32 SYSTEMS
echo KERNEL32 இல்லாத அமைப்புகளுக்கான முழுமையான EXECUTABLE செயல்முறை
echo.

echo This process will create a fully compatible executable that works
echo without kernel32.dll on very old Windows systems.
echo.
echo இந்த செயல்முறை மிகவும் பழைய Windows அமைப்புகளில் kernel32.dll
echo இல்லாமல் வேலை செய்யும் முழுமையாக இணக்கமான executable ஐ உருவாக்கும்.
echo.

REM Check prerequisites
echo [STEP 1] Checking prerequisites...
echo [படி 1] முன்நிபந்தனைகளைச் சரிபார்க்கிறது...
echo.

if not exist "package.json" (
    echo ✗ package.json not found. Please run this from the project root.
    echo ✗ package.json கிடைக்கவில்லை. திட்ட root இலிருந்து இதை இயக்கவும்.
    pause
    exit /b 1
)

if not exist "src\index.js" (
    echo ✗ Main source files not found
    echo ✗ முக்கிய மூல கோப்புகள் கிடைக்கவில்லை
    pause
    exit /b 1
)

echo ✓ Prerequisites check passed
echo ✓ முன்நிபந்தனை சரிபார்ப்பு வெற்றியடைந்தது

echo.
echo [STEP 2] Building no-kernel32 version...
echo [படி 2] Kernel32 இல்லாத பதிப்பை உருவாக்குகிறது...
echo.

if exist "build-no-kernel32.bat" (
    call build-no-kernel32.bat
    if %ERRORLEVEL% NEQ 0 (
        echo ✗ Build failed
        pause
        exit /b 1
    )
    echo ✓ Build completed
) else (
    echo ✗ Build script not found
    pause
    exit /b 1
)

echo.
echo [STEP 3] Processing executable...
echo [படி 3] Executable ஐ செயலாக்குகிறது...
echo.

if exist "process-exe-no-kernel32.bat" (
    call process-exe-no-kernel32.bat
    if %ERRORLEVEL% NEQ 0 (
        echo ✗ Processing failed
        pause
        exit /b 1
    )
    echo ✓ Processing completed
) else (
    echo ✗ Processing script not found
    pause
    exit /b 1
)

echo.
echo [STEP 4] Creating final distribution...
echo [படி 4] இறுதி விநியோகத்தை உருவாக்குகிறது...
echo.

set "FINAL_DIR=final-no-kernel32-exe"
set "PROCESS_ID=a550c9cf-a239-4431-805f-642e298115ef"

if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%" >nul 2>&1
mkdir "%FINAL_DIR%" >nul 2>&1

REM Copy the best version for distribution
if exist "processed-exe\portable" (
    xcopy "processed-exe\portable\*" "%FINAL_DIR%\" /E /I /H /Y >nul 2>&1
    echo ✓ Portable version copied to final distribution
)

REM Create a simple launcher
echo @echo off > "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"
echo REM Tamil Jadhagam Manager - No Kernel32 Launcher >> "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"
echo REM Process ID: %PROCESS_ID% >> "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"
echo echo Starting Tamil Jadhagam Manager... >> "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"
echo echo தமிழ் ஜாதகம் மேலாண்மையைத் தொடங்குகிறது... >> "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"
echo start "" "tamil-jadhagam-manager-no-kernel32.exe" >> "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat"

REM Create installation instructions
echo Tamil Jadhagam Manager - No Kernel32 Version > "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo Process ID: %PROCESS_ID% >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ================================================ >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo FOR SYSTEMS WITHOUT KERNEL32.DLL >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo KERNEL32.DLL இல்லாத அமைப்புகளுக்கு >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo QUICK START: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo விரைவு தொடக்கம்: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 1. Copy this entire folder to your computer >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 2. Double-click START-TAMIL-JADHAGAM.bat >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 3. Or run tamil-jadhagam-manager-no-kernel32.exe directly >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 1. இந்த முழு அடைவையும் உங்கள் கணினியில் நகலெடுக்கவும் >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 2. START-TAMIL-JADHAGAM.bat ஐ இரட்டை கிளிக் செய்யவும் >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 3. அல்லது tamil-jadhagam-manager-no-kernel32.exe ஐ நேரடியாக இயக்கவும் >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo SYSTEM REQUIREMENTS: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo கணினி தேவைகள்: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ✅ Windows XP SP3 or later >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ✅ 512MB RAM minimum >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ✅ 200MB free space >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ✅ NO kernel32.dll required >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo ✅ NO additional software needed >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo TROUBLESHOOTING: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo சிக்கல் निवारण: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo If the application doesn't start: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo பயன்பாடு தொடங்கவில்லை என்றால்: >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo. >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 1. Try run-compatibility-mode.bat >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 2. Run check-system-compatibility.bat >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 3. Make sure you have enough free space >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"
echo 4. Try running as administrator >> "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt"

echo ✓ Final distribution created
echo ✓ இறுதி விநியோகம் உருவாக்கப்பட்டது

echo.
echo [STEP 5] Final verification...
echo [படி 5] இறுதி சரிபார்ப்பு...
echo.

if exist "%FINAL_DIR%\tamil-jadhagam-manager-no-kernel32.exe" (
    echo ✓ Main executable present
    for %%A in ("%FINAL_DIR%\tamil-jadhagam-manager-no-kernel32.exe") do (
        echo   Size: %%~zA bytes
        echo   Date: %%~tA
    )
) else (
    echo ✗ Main executable missing
    pause
    exit /b 1
)

if exist "%FINAL_DIR%\START-TAMIL-JADHAGAM.bat" (
    echo ✓ Launcher script present
) else (
    echo ✗ Launcher script missing
)

if exist "%FINAL_DIR%\INSTALLATION-INSTRUCTIONS.txt" (
    echo ✓ Installation instructions present
) else (
    echo ✗ Installation instructions missing
)

echo.
echo ========================================
echo PROCESS COMPLETED SUCCESSFULLY!
echo செயல்முறை வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.
echo Process ID: %PROCESS_ID%
echo.

echo FINAL EXECUTABLE LOCATION:
echo இறுதி EXECUTABLE இடம்:
echo.
echo 📁 Directory: %FINAL_DIR%\
echo 🚀 Main File: tamil-jadhagam-manager-no-kernel32.exe
echo 🎯 Launcher: START-TAMIL-JADHAGAM.bat
echo 📖 Instructions: INSTALLATION-INSTRUCTIONS.txt
echo.

echo DISTRIBUTION READY:
echo விநியோகத்திற்கு தயார்:
echo.
echo ✅ Works without kernel32.dll
echo ✅ Compatible with Windows XP SP3+
echo ✅ No additional dependencies
echo ✅ Portable - can run from any location
echo ✅ Includes Tamil language support
echo ✅ JSON database (no native SQLite)
echo ✅ Ultra-compatible Electron 9.4.4
echo.

echo NEXT STEPS:
echo அடுத்த படிகள்:
echo.
echo 1. Test the executable on your target system
echo 2. Copy the entire %FINAL_DIR%\ folder to target systems
echo 3. Run START-TAMIL-JADHAGAM.bat or the .exe directly
echo 4. Distribute to users who have kernel32.dll issues
echo.
echo 1. உங்கள் இலக்கு கணினியில் executable ஐ சோதிக்கவும்
echo 2. முழு %FINAL_DIR%\ அடைவையும் இலக்கு கணினிகளுக்கு நகலெடுக்கவும்
echo 3. START-TAMIL-JADHAGAM.bat அல்லது .exe ஐ நேரடியாக இயக்கவும்
echo 4. kernel32.dll சிக்கல்கள் உள்ள பயனர்களுக்கு விநியோகிக்கவும்
echo.

echo ========================================
echo YOUR NO-KERNEL32 EXECUTABLE IS READY!
echo உங்கள் KERNEL32 இல்லாத EXECUTABLE தயார்!
echo ========================================
echo.

pause
