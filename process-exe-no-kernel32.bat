@echo off
REM Tamil Jadhagam Manager - EXE Processing Script for No-Kernel32 Version
REM தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத பதிப்புக்கான EXE செயலாக்க ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - EXE Processing
echo தமிழ் ஜாதகம் மேலாண்மை - EXE செயலாக்கம்
echo ========================================
echo.
echo Processing executable for systems WITHOUT kernel32.dll
echo Kernel32.dll இல்லாத அமைப்புகளுக்கான executable செயலாக்கம்
echo.

REM Define paths
set "SOURCE_EXE=out\Tamil Jadhagam Manager No-Kernel32-win32-ia32\tamil-jadhagam-manager-no-kernel32.exe"
set "PROCESSED_DIR=processed-exe"
set "PORTABLE_DIR=%PROCESSED_DIR%\portable"
set "STANDALONE_DIR=%PROCESSED_DIR%\standalone"

echo [1/10] Checking source executable...
echo மூல executable ஐ சரிபார்க்கிறது...
echo.

if not exist "%SOURCE_EXE%" (
    echo ✗ Source executable not found: %SOURCE_EXE%
    echo ✗ மூல executable கிடைக்கவில்லை: %SOURCE_EXE%
    echo.
    echo Please build the no-kernel32 version first:
    echo முதலில் no-kernel32 பதிப்பை உருவாக்கவும்:
    echo   build-no-kernel32.bat
    echo.
    pause
    exit /b 1
)

echo ✓ Source executable found
echo ✓ மூல executable கண்டுபிடிக்கப்பட்டது

REM Get file information
for %%A in ("%SOURCE_EXE%") do (
    set "EXE_SIZE=%%~zA"
    set "EXE_DATE=%%~tA"
)

echo   Size: %EXE_SIZE% bytes
echo   Date: %EXE_DATE%

echo [2/10] Creating processing directories...
echo செயலாக்க அடைவுகளை உருவாக்குகிறது...
echo.

REM Create directories
if exist "%PROCESSED_DIR%" rmdir /s /q "%PROCESSED_DIR%" >nul 2>&1
mkdir "%PROCESSED_DIR%" >nul 2>&1
mkdir "%PORTABLE_DIR%" >nul 2>&1
mkdir "%STANDALONE_DIR%" >nul 2>&1

echo ✓ Processing directories created
echo ✓ செயலாக்க அடைவுகள் உருவாக்கப்பட்டன

echo [3/10] Creating portable version...
echo Portable பதிப்பை உருவாக்குகிறது...
echo.

REM Copy entire application folder for portable version
xcopy "out\Tamil Jadhagam Manager No-Kernel32-win32-ia32\*" "%PORTABLE_DIR%\" /E /I /H /Y >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Portable version created
    echo ✓ Portable பதிப்பு உருவாக்கப்பட்டது
) else (
    echo ✗ Failed to create portable version
    echo ✗ Portable பதிப்பை உருவாக்க முடியவில்லை
)

echo [4/10] Creating standalone executable...
echo Standalone executable ஐ உருவாக்குகிறது...
echo.

REM Copy just the executable for standalone version
copy "%SOURCE_EXE%" "%STANDALONE_DIR%\tamil-jadhagam-no-kernel32.exe" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Standalone executable created
    echo ✓ Standalone executable உருவாக்கப்பட்டது
) else (
    echo ✗ Failed to create standalone executable
    echo ✗ Standalone executable ஐ உருவாக்க முடியவில்லை
)

echo [5/10] Analyzing executable dependencies...
echo Executable சார்புகளை பகுப்பாய்வு செய்கிறது...
echo.

REM Check if we have dependency walker or similar tools
where dumpbin >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Found dumpbin tool for dependency analysis
    dumpbin /dependents "%SOURCE_EXE%" > "%PROCESSED_DIR%\dependencies.txt" 2>&1
    echo ✓ Dependency analysis saved to dependencies.txt
) else (
    echo ! Dependency analysis tools not available
    echo ! சார்பு பகுப்பாய்வு கருவிகள் கிடைக்கவில்லை
)

echo [6/10] Creating compatibility batch files...
echo இணக்கத்தன்மை batch கோப்புகளை உருவாக்குகிறது...
echo.

REM Create run script for portable version
echo @echo off > "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo REM Tamil Jadhagam Manager - No Kernel32 Version >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo REM தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத பதிப்பு >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo. >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo echo Starting Tamil Jadhagam Manager (No Kernel32)... >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo echo தமிழ் ஜாதகம் மேலாண்மையைத் தொடங்குகிறது (Kernel32 இல்லாமல்)... >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo. >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"
echo start "" "tamil-jadhagam-manager-no-kernel32.exe" >> "%PORTABLE_DIR%\run-tamil-jadhagam.bat"

REM Create compatibility mode script
echo @echo off > "%PORTABLE_DIR%\run-compatibility-mode.bat"
echo REM Run in Windows XP Compatibility Mode >> "%PORTABLE_DIR%\run-compatibility-mode.bat"
echo echo Running in Windows XP compatibility mode... >> "%PORTABLE_DIR%\run-compatibility-mode.bat"
echo echo Windows XP இணக்கத்தன்மை பயன்முறையில் இயங்குகிறது... >> "%PORTABLE_DIR%\run-compatibility-mode.bat"
echo. >> "%PORTABLE_DIR%\run-compatibility-mode.bat"
echo start "" /COMPAT:WINXPSP3 "tamil-jadhagam-manager-no-kernel32.exe" >> "%PORTABLE_DIR%\run-compatibility-mode.bat"

echo ✓ Compatibility batch files created
echo ✓ இணக்கத்தன்மை batch கோப்புகள் உருவாக்கப்பட்டன

echo [7/10] Creating documentation files...
echo ஆவண கோப்புகளை உருவாக்குகிறது...
echo.

REM Create README for portable version
echo Tamil Jadhagam Manager - No Kernel32 Version > "%PORTABLE_DIR%\README.txt"
echo தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத பதிப்பு >> "%PORTABLE_DIR%\README.txt"
echo. >> "%PORTABLE_DIR%\README.txt"
echo SPECIAL VERSION FOR SYSTEMS WITHOUT KERNEL32.DLL >> "%PORTABLE_DIR%\README.txt"
echo KERNEL32.DLL இல்லாத அமைப்புகளுக்கான சிறப்பு பதிப்பு >> "%PORTABLE_DIR%\README.txt"
echo. >> "%PORTABLE_DIR%\README.txt"
echo HOW TO RUN: >> "%PORTABLE_DIR%\README.txt"
echo எப்படி இயக்குவது: >> "%PORTABLE_DIR%\README.txt"
echo. >> "%PORTABLE_DIR%\README.txt"
echo 1. Double-click: run-tamil-jadhagam.bat >> "%PORTABLE_DIR%\README.txt"
echo 2. Or directly run: tamil-jadhagam-manager-no-kernel32.exe >> "%PORTABLE_DIR%\README.txt"
echo 3. For compatibility issues: run-compatibility-mode.bat >> "%PORTABLE_DIR%\README.txt"
echo. >> "%PORTABLE_DIR%\README.txt"
echo SYSTEM REQUIREMENTS: >> "%PORTABLE_DIR%\README.txt"
echo கணினி தேவைகள்: >> "%PORTABLE_DIR%\README.txt"
echo. >> "%PORTABLE_DIR%\README.txt"
echo - Windows XP SP3 or later >> "%PORTABLE_DIR%\README.txt"
echo - 512MB RAM minimum >> "%PORTABLE_DIR%\README.txt"
echo - 200MB free disk space >> "%PORTABLE_DIR%\README.txt"
echo - No additional dependencies >> "%PORTABLE_DIR%\README.txt"
echo - No Visual C++ Redistributables needed >> "%PORTABLE_DIR%\README.txt"
echo - Works without kernel32.dll >> "%PORTABLE_DIR%\README.txt"

echo ✓ Documentation files created
echo ✓ ஆவண கோப்புகள் உருவாக்கப்பட்டன

echo [8/10] Creating system information script...
echo கணினி தகவல் ஸ்கிரிப்டை உருவாக்குகிறது...
echo.

REM Create system info script
echo @echo off > "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo Tamil Jadhagam Manager - System Compatibility Check >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo தமிழ் ஜாதகம் மேலாண்மை - கணினி இணக்கத்தன்மை சரிபார்ப்பு >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo ================================================== >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo Windows Version: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo ver >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo System Architecture: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo wmic os get osarchitecture /value 2^>nul ^|^| echo Unknown >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo Total Physical Memory: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo wmic computersystem get TotalPhysicalMemory /value 2^>nul ^|^| echo Unknown >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo Processor: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo wmic cpu get name /value 2^>nul ^|^| echo Unknown >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo ================================================== >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo This version should work on your system if: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo இந்த பதிப்பு உங்கள் கணினியில் வேலை செய்ய வேண்டும் என்றால்: >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo - Windows XP SP3 or later >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo - At least 512MB RAM >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo - x86 or x64 processor >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo echo. >> "%PORTABLE_DIR%\check-system-compatibility.bat"
echo pause >> "%PORTABLE_DIR%\check-system-compatibility.bat"

echo ✓ System compatibility check script created
echo ✓ கணினி இணக்கத்தன்மை சரிபார்ப்பு ஸ்கிரிப்ட் உருவாக்கப்பட்டது

echo [9/10] Creating distribution packages...
echo விநியோக தொகுப்புகளை உருவாக்குகிறது...
echo.

REM Create ZIP packages
where 7z >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Found 7-Zip, creating compressed packages...
    7z a -tzip "%PROCESSED_DIR%\Tamil-Jadhagam-No-Kernel32-Portable.zip" "%PORTABLE_DIR%\*" >nul 2>&1
    7z a -tzip "%PROCESSED_DIR%\Tamil-Jadhagam-No-Kernel32-Standalone.zip" "%STANDALONE_DIR%\*" >nul 2>&1
    echo ✓ ZIP packages created
) else (
    echo ! 7-Zip not found, skipping ZIP creation
    echo ! 7-Zip கிடைக்கவில்லை, ZIP உருவாக்கலைத் தவிர்க்கிறது
)

echo [10/10] Generating final report...
echo இறுதி அறிக்கையை உருவாக்குகிறது...
echo.

REM Create final report
echo Tamil Jadhagam Manager - No Kernel32 EXE Processing Report > "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo தமிழ் ஜாதகம் மேலாண்மை - Kernel32 இல்லாத EXE செயலாக்க அறிக்கை >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ================================================================ >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo Processing Date: %DATE% %TIME% >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo Source Executable: %SOURCE_EXE% >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo Source Size: %EXE_SIZE% bytes >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo Source Date: %EXE_DATE% >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo CREATED PACKAGES: >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo உருவாக்கப்பட்ட தொகுப்புகள்: >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo 1. Portable Version: %PORTABLE_DIR%\ >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo    - Complete application with all files >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo    - Can be run from any location >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo    - Includes helper scripts and documentation >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo 2. Standalone Version: %STANDALONE_DIR%\ >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo    - Single executable file only >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo    - Minimal footprint >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo COMPATIBILITY FEATURES: >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo இணக்கத்தன்மை அம்சங்கள்: >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo. >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ No kernel32.dll dependency >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ Works on Windows XP SP3+ >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ 32-bit architecture for maximum compatibility >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ JSON database (no native SQLite) >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ Ultra-compatible Electron 9.4.4 >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ No Visual C++ Redistributables required >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"
echo ✓ Minimal system requirements (512MB RAM) >> "%PROCESSED_DIR%\PROCESSING_REPORT.txt"

echo.
echo ========================================
echo EXE PROCESSING COMPLETED SUCCESSFULLY!
echo EXE செயலாக்கம் வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.

echo PROCESSED FILES LOCATION:
echo செயலாக்கப்பட்ட கோப்புகளின் இடம்:
echo.
echo Main Directory: %PROCESSED_DIR%\
echo முக்கிய அடைவு: %PROCESSED_DIR%\
echo.

if exist "%PORTABLE_DIR%" (
    echo ✓ Portable Version: %PORTABLE_DIR%\
    echo   Files: Complete application folder
    echo   Usage: Run run-tamil-jadhagam.bat or the .exe directly
    echo   Size: Full application (~120MB)
)

if exist "%STANDALONE_DIR%" (
    echo ✓ Standalone Version: %STANDALONE_DIR%\
    echo   Files: Single executable only
    echo   Usage: Run tamil-jadhagam-no-kernel32.exe
    echo   Size: Minimal (~80MB)
)

if exist "%PROCESSED_DIR%\Tamil-Jadhagam-No-Kernel32-Portable.zip" (
    echo ✓ Portable ZIP: Tamil-Jadhagam-No-Kernel32-Portable.zip
)

if exist "%PROCESSED_DIR%\Tamil-Jadhagam-No-Kernel32-Standalone.zip" (
    echo ✓ Standalone ZIP: Tamil-Jadhagam-No-Kernel32-Standalone.zip
)

echo.
echo DISTRIBUTION READY FILES:
echo விநியோகத்திற்கு தயார் கோப்புகள்:
echo.
echo FOR END USERS (systems without kernel32.dll):
echo இறுதி பயனர்களுக்கு (kernel32.dll இல்லாத அமைப்புகள்):
echo.
echo 1. PORTABLE VERSION (Recommended):
echo    📁 %PORTABLE_DIR%\
echo    📄 Contains: Complete application + helper scripts
echo    🚀 Usage: Extract and run run-tamil-jadhagam.bat
echo    💾 Size: ~120MB
echo    ✅ Best for: Users who want full functionality
echo.
echo 2. STANDALONE VERSION (Minimal):
echo    📁 %STANDALONE_DIR%\
echo    📄 Contains: Single executable file
echo    🚀 Usage: Just run the .exe file
echo    💾 Size: ~80MB
echo    ✅ Best for: Users who want minimal installation
echo.

echo SYSTEM COMPATIBILITY:
echo கணினி இணக்கத்தன்மை:
echo.
echo ✅ Windows XP SP3 or later
echo ✅ 512MB RAM minimum (1GB recommended)
echo ✅ 200MB free disk space
echo ✅ Any x86 processor (Pentium 4+)
echo ✅ No kernel32.dll required
echo ✅ No Visual C++ Redistributables needed
echo ✅ No .NET Framework required
echo ✅ Works on very old systems
echo ✅ Compatible with embedded Windows
echo.

echo TROUBLESHOOTING HELPERS INCLUDED:
echo சிக்கல் निवारण உதவியாளர்கள் சேர்க்கப்பட்டுள்ளன:
echo.
echo 📋 check-system-compatibility.bat - Check if system meets requirements
echo 🔧 run-compatibility-mode.bat - Run in Windows XP compatibility mode
echo 📖 README.txt - Complete usage instructions
echo 📊 PROCESSING_REPORT.txt - Technical details and build information
echo.

echo NEXT STEPS:
echo அடுத்த படிகள்:
echo.
echo 1. Test the executable on your target system
echo 2. Distribute the appropriate version to users
echo 3. Provide the README.txt file with instructions
echo 4. Use compatibility scripts if needed
echo.
echo 1. உங்கள் இலக்கு கணினியில் executable ஐ சோதிக்கவும்
echo 2. பயனர்களுக்கு பொருத்தமான பதிப்பை விநியோகிக்கவும்
echo 3. அறிவுறுத்தல்களுடன் README.txt கோப்பை வழங்கவும்
echo 4. தேவைப்பட்டால் இணக்கத்தன்மை ஸ்கிரிப்ட்களைப் பயன்படுத்தவும்
echo.

echo ========================================
echo PROCESSING COMPLETE!
echo செயலாக்கம் முடிந்தது!
echo ========================================
echo.
echo Your no-kernel32 executable is ready for distribution!
echo உங்கள் kernel32 இல்லாத executable விநியோகத்திற்கு தயார்!
echo.

pause
