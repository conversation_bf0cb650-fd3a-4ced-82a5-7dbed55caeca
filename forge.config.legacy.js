const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: false, // Disable ASAR for maximum compatibility
    name: 'Tamil Jadhagam Manager Legacy',
    executableName: 'tamil-jadhagam-manager-legacy',
    appBundleId: 'com.tamiljadhagam.manager.legacy',
    appCategoryType: 'public.app-category.productivity',
    icon: './src/assets/app-icon.png',
    arch: 'ia32', // 32-bit for legacy systems
    platform: 'win32',
    electronVersion: '11.5.0', // Ultra-compatible version for systems without kernel32.dll
    win32metadata: {
      CompanyName: 'Tamil Jadhagam Solutions',
      ProductName: 'Tamil Jadhagam Manager (Legacy)',
      FileDescription: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் மேலாண்மை (Legacy)',
      OriginalFilename: 'tamil-jadhagam-manager-legacy.exe',
      RequestedExecutionLevel: 'asInvoker', // Don't require admin
      'requested-execution-level': 'asInvoker',
      'application-manifest': false
    },
    // Additional compatibility options for systems without kernel32.dll
    extraResource: [],
    ignore: [
      /node_modules\/(?!sqlite3)/,
      /\.git/,
      /\.vscode/,
      /\.DS_Store/,
      /Thumbs\.db/
    ],
    // Force static linking to avoid kernel32.dll dependencies
    buildVersion: '1.0.0',
    protocols: [],
    // Disable features that require newer Windows APIs
    enableLogging: false,
    // Use older Windows subsystem
    win32metadata: {
      CompanyName: 'Tamil Jadhagam Solutions',
      ProductName: 'Tamil Jadhagam Manager (Legacy)',
      FileDescription: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் மேலாண்மை (Legacy)',
      OriginalFilename: 'tamil-jadhagam-manager-legacy.exe',
      RequestedExecutionLevel: 'asInvoker',
      'requested-execution-level': 'asInvoker',
      'application-manifest': false,
      'compatibility-mode': 'windows7'
    }
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'tamil-jadhagam-manager-legacy',
        setupExe: 'Tamil-Jadhagam-Manager-Legacy-Setup.exe',
        authors: 'Tamil Jadhagam Solutions',
        description: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் ஆவண மேலாண்மை அமைப்பு (Legacy)',
        noMsi: true, // Disable MSI for compatibility
        setupIcon: './src/assets/app-icon.png',
        loadingGif: false, // Disable loading gif for compatibility
        skipUpdateIcon: true,
        certificateFile: false,
        certificatePassword: false
      },
      platforms: ['win32']
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['win32'],
    },
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {},
    },
    // Minimal fuses for maximum compatibility
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: false, // Disable for compatibility
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: false, // Disable since ASAR is off
      [FuseV1Options.OnlyLoadAppFromAsar]: false, // Disable since ASAR is off
    }),
  ],
};
