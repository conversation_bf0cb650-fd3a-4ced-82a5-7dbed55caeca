{"name": "tamil-jadhagam-app", "productName": "Tamil Jadhagam Manager", "version": "1.3.4", "description": "Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் ஆவண மேலாண்மை அமைப்பு", "main": "src/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\"", "build": "electron-forge package", "dist": "electron-forge make", "dist-32": "electron-forge make --config=./forge.config.32bit.js", "dist-64": "electron-forge make --arch=x64", "dist-all": "npm run dist-64 && npm run dist-32", "build-32": "electron-forge package --config=./forge.config.32bit.js", "build-legacy": "electron-forge package --config=./forge.config.legacy.js", "dist-legacy": "electron-forge make --config=./forge.config.legacy.js", "build-no-kernel32": "electron-forge package --config=./forge.config.no-kernel32.js", "dist-no-kernel32": "electron-forge make --config=./forge.config.no-kernel32.js", "build-all-compat": "npm run dist-64 && npm run dist-32 && npm run dist-legacy && npm run dist-no-kernel32", "process-no-kernel32": "complete-no-kernel32-process.bat", "verify-no-kernel32": "verify-no-kernel32-exe.bat"}, "keywords": ["tamil", "<PERSON><PERSON><PERSON><PERSON>", "horoscope", "astrology", "document-management"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "multer": "^2.0.1", "path-browserify": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sqlite3": "^5.1.7"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-react": "^7.27.1", "@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "electron": "22.3.27", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "webpack": "^5.100.1", "webpack-cli": "^6.0.1"}}