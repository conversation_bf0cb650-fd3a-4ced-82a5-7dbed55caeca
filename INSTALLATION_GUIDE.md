# Tamil Jadhagam Manager - Installation Guide
## தமிழ் ஜாதகம் மேலாண்மை - நிறுவல் வழிகாட்டி

### Quick Installation - விரைவு நிறுவல்

1. **Download the Installer**
   - Navigate to: `tamil-jadhagam-app/out/make/squirrel.windows/x64/`
   - Download: `Tamil-Jadhagam-Manager-Setup.exe`

2. **Install the Application**
   - Right-click on `Tamil-Jadhagam-Manager-Setup.exe`
   - Select "Run as administrator" (recommended)
   - Follow the installation wizard
   - The app will be installed to: `%LOCALAPPDATA%/tamil-jadhagam-manager/`

3. **Launch the Application**
   - Find "Tamil Jadhagam Manager" in Start Menu
   - Or use the desktop shortcut (if created)
   - Or run from: `%LOCALAPPDATA%/tamil-jadhagam-manager/tamil-jadhagam-manager.exe`

### System Requirements - கணினி தேவைகள்

- **Operating System**: Windows 10 or later (64-bit)
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 500MB free space for application + space for documents
- **Display**: 1024x768 minimum resolution
- **Internet**: Required for Tamil font download (first run only)

### First Time Setup - முதல் முறை அமைப்பு

1. **Tamil Font Installation**
   - The app uses "Noto Sans Tamil" font
   - If not available, it will fall back to system fonts
   - For best experience, install Tamil fonts from Google Fonts

2. **Database Initialization**
   - On first run, the app creates a SQLite database
   - Location: `%APPDATA%/Tamil Jadhagam Manager/jadhagam.db`
   - Pre-populated with Tamil astrological data

3. **Document Storage**
   - Documents are stored in: `%APPDATA%/Tamil Jadhagam Manager/documents/`
   - Automatic backup recommended for this folder

### Features Overview - அம்சங்கள் மேலோட்டம்

#### Upload Tab - பதிவேற்றம் தாவல்
- **Personal Information**: Name, Gender, Birth details
- **Astrological Data**: Rasi (12 options), Nathathiram (27 options)
- **Social Information**: Jadthi, City (Tamil Nadu cities)
- **File Upload**: PDF, JPG, PNG, GIF formats supported
- **Additional Notes**: Free text field for extra information

#### Search Tab - தேடல் தாவல்
- **Multi-criteria Search**: Name, Rasi, Nathathiram, Jadthi, City
- **Partial Matching**: Search works with partial text
- **Tamil Text Support**: Search in Tamil or English
- **Clear Function**: Reset all search fields

#### Results Tab - முடிவுகள் தாவல்
- **Detailed Display**: All stored information shown
- **Document Access**: One-click to open original files
- **Date Information**: Upload date and birth date
- **Organized Layout**: Easy-to-read card format

### Usage Instructions - பயன்பாட்டு வழிமுறைகள்

#### Adding a New Jadhagam - புதிய ஜாதகம் சேர்த்தல்

1. **Click "பதிவேற்றம் - Upload" tab**
2. **Fill Required Fields** (marked with *):
   - பெயர் - Name
   - ராசி - Rasi (select from dropdown)
   - நட்சத்திரம் - Nathathiram (select from dropdown)
   - ஜாதி - Jadthi (select from dropdown)
   - நகரம் - City (select from dropdown)

3. **Fill Optional Fields**:
   - பாலினம் - Gender
   - பிறந்த தேதி - Birth Date
   - பிறந்த நேரம் - Birth Time
   - தந்தையின் பெயர் - Father's Name
   - தாயின் பெயர் - Mother's Name
   - குறிப்புகள் - Notes

4. **Click "ஜாதகம் பதிவேற்றம் - Upload Jadhagam"**
5. **Select Document File** from file dialog
6. **Confirm Upload** - Success message will appear

#### Searching for Jadhagam - ஜாதகம் தேடுதல்

1. **Click "தேடல் - Search" tab**
2. **Enter Search Criteria** (any combination):
   - Name (partial matching supported)
   - Select Rasi, Nathathiram, Jadthi, or City
   - Leave fields empty to search all

3. **Click "தேடல் - Search"**
4. **View Results** in "முடிவுகள் - Results" tab
5. **Open Documents** by clicking "ஆவணத்தைத் திற - Open Document"

### Data Management - தரவு மேலாண்மை

#### Backup Your Data - உங்கள் தரவை காப்புப் பிரதி எடுக்கவும்

**Important Folders to Backup**:
```
%APPDATA%/Tamil Jadhagam Manager/
├── jadhagam.db (database file)
└── documents/ (uploaded files)
```

**Backup Steps**:
1. Close the Tamil Jadhagam Manager application
2. Navigate to `%APPDATA%/Tamil Jadhagam Manager/`
3. Copy the entire folder to external drive or cloud storage
4. Schedule regular backups (weekly recommended)

#### Restore Data - தரவை மீட்டமைக்கவும்

1. Close the application
2. Replace the folder contents with backup
3. Restart the application
4. Verify data integrity

### Troubleshooting - சிக்கல் தீர்வு

#### Common Issues - பொதுவான சிக்கல்கள்

**1. Tamil Text Not Displaying Properly**
- Install Tamil fonts from Google Fonts
- Restart the application
- Check Windows language settings

**2. Database Errors**
- Check file permissions in `%APPDATA%`
- Run application as administrator
- Delete database file to reset (data will be lost)

**3. File Upload Fails**
- Check available disk space
- Verify file format (PDF, JPG, PNG, GIF)
- Check file size (recommended < 50MB)

**4. Search Not Working**
- Verify database connection
- Check for special characters in search terms
- Try partial name search

**5. Application Won't Start**
- Check Windows compatibility
- Run as administrator
- Reinstall the application

#### Error Messages - பிழை செய்திகள்

- **"Database connection failed"**: Check file permissions
- **"Upload failed"**: Check disk space and file format
- **"Search failed"**: Database connectivity issue
- **"File not found"**: Document may have been moved/deleted

### Advanced Features - மேம்பட்ட அம்சங்கள்

#### Keyboard Shortcuts
- **Ctrl + 1**: Switch to Upload tab
- **Ctrl + 2**: Switch to Search tab
- **Ctrl + 3**: Switch to Results tab
- **Ctrl + S**: Submit current form
- **Ctrl + R**: Clear search form

#### Data Export (Future Feature)
- Export search results to CSV
- Backup database to external file
- Print-friendly report generation

### Support and Updates - ஆதரவு மற்றும் புதுப்பிப்புகள்

#### Getting Help
- Check this guide first
- Review the README.md file
- Contact technical support

#### Future Updates
- Automatic update notifications
- New features and improvements
- Bug fixes and security updates

### Uninstallation - நிறுவல் நீக்கம்

1. **Using Windows Settings**:
   - Go to Settings > Apps
   - Find "Tamil Jadhagam Manager"
   - Click Uninstall

2. **Manual Cleanup** (if needed):
   - Delete: `%LOCALAPPDATA%/tamil-jadhagam-manager/`
   - Delete: `%APPDATA%/Tamil Jadhagam Manager/`
   - Remove desktop shortcuts

**Note**: Backup your data before uninstalling!

---

**தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager v1.0.0**
*Preserving Tamil astrological heritage through modern technology*
