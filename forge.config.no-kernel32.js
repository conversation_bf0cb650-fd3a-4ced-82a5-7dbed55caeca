const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: false, // Disable ASAR for maximum compatibility
    name: 'Tamil Jadhagam Manager No-Kernel32',
    executableName: 'tamil-jadhagam-manager-no-kernel32',
    appBundleId: 'com.tamiljadhagam.manager.nokernel32',
    appCategoryType: 'public.app-category.productivity',
    icon: './src/assets/app-icon.png',
    arch: 'ia32', // 32-bit for maximum compatibility
    platform: 'win32',
    electronVersion: '9.4.4', // Very old version that doesn't require kernel32.dll
    win32metadata: {
      CompanyName: 'Tamil Jadhagam Solutions',
      ProductName: 'Tamil Jadhagam Manager (No Kernel32)',
      FileDescription: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் மேலாண்மை (No Kernel32)',
      OriginalFilename: 'tamil-jadhagam-manager-no-kernel32.exe',
      RequestedExecutionLevel: 'asInvoker',
      'requested-execution-level': 'asInvoker',
      'application-manifest': false,
      'compatibility-mode': 'windowsxp'
    },
    // Force static linking and avoid dynamic dependencies
    extraResource: [],
    ignore: [
      /node_modules\/(?!(sqlite3|better-sqlite3))/,
      /\.git/,
      /\.vscode/,
      /\.DS_Store/,
      /Thumbs\.db/,
      /test/,
      /docs/,
      /\.md$/
    ],
    // Additional options to avoid kernel32.dll
    buildVersion: '1.0.0',
    protocols: [],
    enableLogging: false,
    // Disable modern Windows features
    afterCopy: [
      (buildPath, electronVersion, platform, arch, callback) => {
        // Custom post-processing to remove kernel32.dll dependencies
        console.log('Post-processing for no-kernel32 compatibility...');
        callback();
      }
    ]
  },
  rebuildConfig: {
    // Force rebuild of native modules for compatibility
    force: true,
    onlyModules: ['sqlite3']
  },
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'tamil-jadhagam-manager-no-kernel32',
        setupExe: 'Tamil-Jadhagam-Manager-No-Kernel32-Setup.exe',
        authors: 'Tamil Jadhagam Solutions',
        description: 'Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் ஆவண மேலாண்மை அமைப்பு (No Kernel32)',
        noMsi: true, // Disable MSI for compatibility
        setupIcon: './src/assets/app-icon.png',
        loadingGif: false, // Disable loading gif for compatibility
        skipUpdateIcon: true,
        certificateFile: false,
        certificatePassword: false,
        // Additional compatibility options
        remoteReleases: false,
        usePackageJson: false,
        // Force older installer format
        noDelta: true,
        setupMsi: false
      },
      platforms: ['win32']
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['win32'],
    },
  ],
  plugins: [
    // Minimal plugins for maximum compatibility
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {
        // Unpack native modules to avoid dynamic loading issues
        unpackNativeModules: true
      },
    },
    // Minimal fuses for systems without kernel32.dll
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: false, // Disable for compatibility
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: false, // Disable since ASAR is off
      [FuseV1Options.OnlyLoadAppFromAsar]: false, // Disable since ASAR is off
    }),
  ],
};
