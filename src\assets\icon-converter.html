<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamil Jadhagam Icon Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-size {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .icon-size h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        canvas {
            border: 1px solid #ccc;
            background: white;
        }
        .download-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #e55a2b;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tamil Jadhagam Manager - Icon Generator</h1>
        <p>This tool converts the SVG icon to PNG format for use as application icon.</p>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>The SVG icon will be automatically loaded and converted to different sizes</li>
                <li>Click "Download PNG" for each size you need</li>
                <li>Save the 256x256 version as "app-icon.png" in the assets folder</li>
                <li>For Windows ICO format, use online converters like convertio.co</li>
            </ol>
        </div>

        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(16)">Download PNG</button>
            </div>
            
            <div class="icon-size">
                <h3>32x32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(32)">Download PNG</button>
            </div>
            
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(48)">Download PNG</button>
            </div>
            
            <div class="icon-size">
                <h3>64x64</h3>
                <canvas id="canvas64" width="64" height="64"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(64)">Download PNG</button>
            </div>
            
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(128)">Download PNG</button>
            </div>
            
            <div class="icon-size">
                <h3>256x256</h3>
                <canvas id="canvas256" width="256" height="256"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(256)">Download PNG</button>
            </div>
        </div>
    </div>

    <script>
        // SVG content with Tamil letter ஜ
        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f7931e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e55a2b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="letterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f9fa;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)" stroke="#d4491f" stroke-width="3"/>
  <circle cx="128" cy="128" r="110" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  <text x="128" y="148" text-anchor="middle" font-family="Noto Sans Tamil, serif" font-size="120" font-weight="bold" fill="url(#letterGradient)">ஜ</text>
  <text x="128" y="220" text-anchor="middle" fill="#ffffff" font-family="Noto Sans Tamil, serif" font-weight="600" font-size="14" opacity="0.9">ஜாதகம்</text>
  <circle cx="128" cy="128" r="123" fill="none" stroke="rgba(0,0,0,0.1)" stroke-width="1"/>
</svg>`;

        function loadSVGToCanvas(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `tamil-jadhagam-icon-${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // Load all sizes when page loads
        window.onload = function() {
            [16, 32, 48, 64, 128, 256].forEach(size => {
                setTimeout(() => loadSVGToCanvas(size), 100);
            });
        };
    </script>
</body>
</html>
